# Unified Branch Comparison: local-teoh-task vs main

Date: 2025-10-22
Base: origin/main (7498fcc3)
Head: local-teoh-task (unified from local-jz-main, local-lai-main, local-too-task)

## 1️⃣ Overview of Merged Branch

This unified branch consolidates three feature branches to deliver Delivery Region public APIs, region-aware product listing, admin product form improvements (variation images and manual postcodes), a new media table migration, and an admin bulk import workflow for order shipping info, along with minor UI and routing cleanups.

Key themes (with branch of origin):
- Delivery Region API endpoints (/api/delivery) — local-jz-main, local-lai-main
- Product listing filter by delivery region (region_id) — local-jz-main
- Admin product variation image management (preview, delete, upload) — local-jz-main
- Admin product manual postcode handling fix — local-lai-main
- Media table migration (media library-like schema) — local-jz-main
- Admin bulk import for orders (postcode, tracking, delivery method) — local-too-task
- UI cleanups and route name normalization — local-jz-main
- Local environment example (.env.local.example) — local-jz-main

## 2️⃣ Combined Summary of Changes

Files added/modified across merged branches:
- M app/Http/Controllers/Admin/ProductsController.php
  - Merge of: variation image operations (local-jz-main) + manual_postcode overwrite fix with feature flag (local-lai-main)
- A app/Http/Controllers/Api/ApiDeliveryRegion.php — new controller for delivery region endpoints (local-jz-main, local-lai-main)
- M app/Http/Controllers/Api/ApiProducts.php — add optional region filter to listing (local-jz-main)
- A database/migrations/2025_10_18_181232_create_media_table.php — create media table (local-jz-main)
- M resources/views/product-form-details.blade.php
  - Merge of: variation image hidden inputs/preview/remove flow (local-jz-main) + repositioned hidden template row for manual postcodes and duplicate removal (local-lai-main)
- M routes/api.php — add /api/delivery routes group to ApiDeliveryRegion (local-jz-main, local-lai-main)
- A .env.local.example — local development example env (local-jz-main)
- M resources/views/include/app.blade.php — remove temporary testing link (local-jz-main)
- M resources/views/login.blade.php — remove temporary TEST wording (local-jz-main)
- M routes/web.php — route name normalization for order receipt (local-jz-main) and add order import routes (local-too-task)
- A app/Http/Controllers/Admin/OrderImportController.php — import flow orchestration (local-too-task)
- A app/Imports/OrderShippingImport.php — Excel import rules/staging (local-too-task)
- A resources/views/order-import.blade.php — upload page (local-too-task)
- A resources/views/order-import-preview.blade.php — preview/confirm page (local-too-task)
- M resources/views/orders.blade.php — add Import button and small UI tweak (local-too-task)

Notes:
- Delivery Region endpoints were implemented in both local-jz-main and local-lai-main; behavior appears aligned and should be deduplicated into a single controller and route group. ✅ Confirmed from both branch docs: endpoints and data shapes overlap consistently; unify under one controller and route group.

## 3️⃣ Consolidated New Features

- Delivery Region API endpoints (prefix: /api/delivery)
  - Controller: App\\Http\\Controllers\\Api\\ApiDeliveryRegion
  - Endpoints:
    - GET /countries → Active countries that have active delivery regions
    - GET /countries/{countryId}/regions → Active regions in a country with products_count
    - GET /regions → All active regions with country info and products_count
    - GET /regions/{id}/postcodes → Postcodes for a region
  - Responses via GlobalFunction::sendDataResponse / sendSimpleResponse
  - ✅ Confirmed: Endpoints and response shapes align across local-jz-main and local-lai-main; deduplicate into a single implementation.
  - ⚠️ Still needs confirmation (local-jz-main, local-lai-main): Constructor reads brand via checkFeatureControl('selector','brand'); confirm whether brand scoping is required for these queries.

- Product listing filter by delivery region
  - Controller: App\\Http\\Controllers\\Api\\ApiProducts@listing
  - New optional query param: region_id
  - When provided: products and their categories are filtered to items linked via deliveryRegions pivot, considering only active products.
  - ⚠️ Needs Developer Confirmation (local-jz-main): Product model has deliveryRegions relationship with pivot delivery_region_id.

- New Media table migration
  - Migration: database/migrations/2025_10_18_181232_create_media_table.php
  - Typical media library schema: polymorphic model reference, uuid, collection_name, file metadata columns, json columns for manipulations/properties, responsive images, order_column (indexed), timestamps.

- Admin bulk import for orders
  - Upload CSV/XLSX/XLS (max 5MB) → preview/validate → confirm/apply updates
  - Routes (web; behind auth + edit_orders):
    - GET /order-import, POST /order-import-upload, GET /order-import-preview,
      POST /order-import-confirm, GET /order-import-template, GET /order-import-cancel
  - Uses Maatwebsite\\Excel and a custom importer to validate/stage rows; session-backed preview; transactional apply.
  - ✅ Confirmed from local-too-task doc: maatwebsite/excel is already present in vendor; no composer changes required.

## 4️⃣ Consolidated Adjustments / Enhancements

- Admin Product Variations image management (local-jz-main)
  - Backend (ProductsController): per-variation delete via delete_variation_image[variationId] = '1'; upload via variation_image[variationId]; deletes old stored file when replacing/removing; logs operations.
  - View: hidden containers to carry file inputs and delete flags; preserve image metadata across modal sessions; thumbnail preview and "Remove image" action that enqueues deletion.
  - ⚠️ Needs Developer Confirmation: Some browsers may drop FileList on cloned inputs; prefer moving (detaching) original input rather than cloning.

- Admin product manual postcode handling (local-lai-main)
  - ProductsController: do not overwrite manual_postcode with empty array unless feature flag checkFeatureControl('legacy','manual_postcode') is enabled; otherwise only set when postcodes non-empty.
  - View: move hidden template <tr> for adding manual postcodes above the loop; remove duplicate; respect $legacyEnabled for disabled/readonly.

- API routes wiring (local-jz-main, local-lai-main)
  - routes/api.php: add Route::prefix('delivery')->controller(ApiDeliveryRegion::class)...
  - ⚠️ Needs Developer Confirmation: For Laravel 8+ without global namespaces, ensure fully qualified class (\\App\\Http\\Controllers\\Api\\ApiDeliveryRegion::class) or add use statement.

- UI text/cleanup (local-jz-main)
  - Remove red "THIS IS TESTING LINK!!!" in navbar; clean testing labels on login view.

- Route name normalization (local-jz-main)
  - testOrderPdfReceipt → orderPdfReceipt; update any references.

- Orders list UI tweak (local-too-task)
  - Add Import button (permission: edit_orders) and increase date-range input font-size to 16px.

- Data normalization and logging (local-too-task)
  - Lowercase delivery_method on confirm; Log::info per order updated with updated_by.

## 5️⃣ Consolidated Behavioral Changes

- API product listing becomes region-aware when region_id is provided, excluding categories without matching active products for that region (local-jz-main).
- Admin product variations: image persists unless explicitly removed or replaced; removal clears DB and deletes file; replacing uploads new and deletes old (local-jz-main).
- Admin product manual postcodes: saving with empty submitted list no longer clears existing DB value when legacy flag is off (local-lai-main).
- Delivery Region public APIs expose normalized region/country/postcode data; regions may include products_count (both branches).
- Admin bulk import: authorized users can batch update orders; only valid/selected rows are applied; no-op rows skipped; transactional apply and session cleared on success (local-too-task).
- Route name change may break callers of testOrderPdfReceipt until updated (local-jz-main).

Operational considerations:
- Potential N+1 for products_count per region; consider withCount('products') (local-lai-main; also relevant to local-jz-main).
- Session payload for bulk import preview can be large; consider limits/pagination (local-too-task).

## 6️⃣ Detected or Potential Merge Conflicts

Source-level overlaps to resolve during merge:
- app/Http/Controllers/Admin/ProductsController.php
  - Both branches modify save/update logic: variation image handling (local-jz-main) and manual_postcode persistence rules (local-lai-main). Ensure both behaviors coexist without overwriting each other.

- resources/views/product-form-details.blade.php
  - Both branches edit this view for variation image UI (local-jz-main) and manual postcode template placement (local-lai-main). Merge markup carefully so hidden inputs for images and the single hidden template row both exist and JS hooks remain intact.

- routes/api.php
  - Both branches add the /api/delivery group. Ensure a single, correct group is registered with the intended namespace/class reference. Prefer fully qualified controller reference or add a use statement. Verify no duplicate route definitions.

- app/Http/Controllers/Api/ApiDeliveryRegion.php
  - Implemented in two branches; deduplicate into one file with union of behaviors/messages. Align response format and error messages.

- routes/web.php
  - local-jz-main renames route to orderPdfReceipt; local-too-task adds order import routes. Verify no collisions and that middleware/permissions are consistent with existing admin route groups.
  - ✅ Confirmed from branch docs: Paths do not collide (order import routes vs renamed receipt route). Still verify middleware/permission alignment during merge.

- Migration: 2025_10_18_181232_create_media_table.php
  - If spatie/laravel-medialibrary or another media table already exists in main or other branches, this may conflict by name or schema.

Functional/assumption conflicts:
- Product model relationship deliveryRegions (local-jz-main) must exist and pivot named delivery_region_id. If missing or differently named, region filter will fail.
- ApiDeliveryRegion brand-scoping: constructor reads brand from route params; currently unused. Clarify intended brand scoping across endpoints.
- File input cloning for variation images may drop files in some browsers. Consider moving inputs instead of cloning.
- Bulk import depends on maatwebsite/excel, session capacity, and orders table fields (order_no, postcode, tracking_number, delivery_method, status).
- .env.local.example must not contain active secrets.

## 7️⃣ Unified Post-Merge Validation Checklist

Functional/API
- [ ] Run migrations and verify media table status; adjust if an existing media table/migrations exist. ⚠️ Needs Developer Confirmation
- [ ] Delivery Region endpoints
  - [ ] GET /api/delivery/countries returns unique active countries with expected shape [{ id, name }].
  - [ ] GET /api/delivery/countries/{countryId}/regions returns active regions with products_count or clear message when none.
  - [ ] GET /api/delivery/regions returns list with country info and products_count.
  - [ ] GET /api/delivery/regions/{id}/postcodes returns array for valid active region; returns failure for invalid/inactive.
  - [ ] Response wrapper format (success, message, data) matches existing API conventions.
- [ ] Product listing with region filter
  - [ ] GET /api/products without region_id returns full set.
  - [ ] GET /api/products?region_id={id} returns only categories/products available in that region.
  - [ ] Confirm Product model relationship deliveryRegions exists with pivot delivery_region_id. ⚠️ Needs Developer Confirmation

Admin UI — Products
- [ ] Variation images: thumbnail shows when present; upload replaces and deletes old file; "Remove image" clears DB and deletes file; no change when untouched; hidden inputs for variation_image[] and delete_variation_image[] submit correctly (test across major browsers). ⚠️ Needs Developer Confirmation
- [ ] Manual postcodes: adding a new row works; when legacy.manual_postcode is OFF and submitted list is empty, existing DB value remains; when ON, behavior matches policy; fields disabled/readonly when $legacyEnabled is false.

Admin UI — Orders Import
- [ ] Routes visible only for authenticated users with edit_orders; Orders page shows Import button accordingly.
- [ ] Upload accepts csv/xlsx/xls, 5MB max; invalid types rejected clearly.
- [ ] Template downloads with headers: order_number, postcode, tracking_number, delivery_method.
- [ ] Preview shows counts (Total/Valid/Invalid), errors, and allows inline edits; valid rows pre-selected; Select All works.
- [ ] Confirm applies only selected valid rows; unchanged rows are skipped; delivery_method saved as lowercase; transaction succeeds and session cleared; logs include updated_by.

Routing/Integration
- [ ] Update any references to route('testOrderPdfReceipt') → route('orderPdfReceipt').
- [ ] API delivery routes resolve correctly (class reference and namespace in routes/api.php) across Laravel version used. ⚠️ Needs Developer Confirmation

Performance/Infra
- [ ] Consider withCount('products') or equivalent to avoid N+1 for products_count in region endpoints. ⚠️ Needs Developer Confirmation
- [ ] Ensure storage symlink (php artisan storage:link) and that variation image URLs resolve under public/storage.
- [ ] Review logs for new Log::info entries (variation image ops and order bulk update) — ensure no sensitive data.

Environment/Config
- [ ] .env.local.example contains only placeholders; rotate/scrub any sensitive values. ⚠️ Needs Developer Confirmation
- [ ] Verify any new env keys (e.g., FILES_BASE_URL, STOPLIGHT_OPENAPI_PATH) are documented and consumed.
- [ ] Confirm config/feature_control.php includes selector.brand and legacy.manual_postcode with correct defaults.
- [ ] Orders table fields exist (order_no, postcode, tracking_number, delivery_method, status). ⚠️ Still needs confirmation
- [ ] Dependency 'maatwebsite/excel' present in vendor. ✅ Confirmed from local-too-task doc

Regression
- [ ] Product create/update flows still succeed in cases: manual_postcode absent, empty, populated.
- [ ] Other API route groups unaffected; no collisions under /api/delivery.
- [ ] Orders page and import pages render without console errors.

Notes
- Items marked “⚠️ Needs Developer Confirmation” require alignment during review to finalize expected behavior and resolve assumptions.
