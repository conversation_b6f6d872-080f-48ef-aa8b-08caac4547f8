<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class UpdateEmailAddressDefaultForUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Update existing null email addresses to "-"
        DB::table('users')
            ->whereNull('email_address')
            ->orWhere('email_address', '')
            ->update(['email_address' => '-']);

        // Modify the column to have a default value of "-"
        Schema::table('users', function (Blueprint $table) {
            $table->string('email_address')->default('-')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert the column back to nullable without default
        Schema::table('users', function (Blueprint $table) {
            $table->string('email_address')->nullable()->change();
        });

        // Optionally revert the "-" values back to null
        DB::table('users')
            ->where('email_address', '-')
            ->update(['email_address' => null]);
    }
}
