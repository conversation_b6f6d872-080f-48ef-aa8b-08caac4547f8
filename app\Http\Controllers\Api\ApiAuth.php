<?php

namespace App\Http\Controllers\Api;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Doctors;
use App\Models\PhoneOtp;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\MembershipTiers;
use Illuminate\Validation\Rule;
use App\Traits\VerificationsTraits;
use App\Http\Controllers\Controller;
use App\Models\RedemptionVouchers;
use App\Models\ReferredUser;
use App\Models\UserMembership;
use App\Models\UserVouchers;
use App\Traits\ReferralTraits;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Notifications\AuthNotification;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Notification;

class ApiAuth extends Controller
{
    use VerificationsTraits, ReferralTraits;

    function __construct()
    {
        $this->brandSelector = checkFeatureControl('selector', 'brand');
    }

    function signup(Request $request)
    {
        $brand = null;

        if ($this->brandSelector) {
            $brand = $request->route()->parameters['brand'];
        }

        $rules = [
            'name' => 'required',
            'email_address' => 'nullable|email',
            'dob' => 'required',
            'phone_number' => 'required',
            // 'race' => 'required',
            // 'preferred_language' => 'required',
            // 'hear_about' => 'required',
            'referral_code' => 'nullable',
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if ($result):
            return $result;
        endif;

        $checkOtpExists = PhoneOtp::when($this->brandSelector && isset($brand), function ($q) use ($brand) {
            $q->where('brand', $brand);
        })
            ->where('phone_number', $request->phone_number)
            ->first();

        if (!isset($checkOtpExists)):
            return GlobalFunction::sendSimpleResponse(false, __('Invalid OTP.'));
        endif;

        $exist = User::where('phone_number', $request->phone_number)
            ->when($this->brandSelector && isset($brand), function ($q) use ($brand) {
                $q->where('brand', $brand);
            })->whereNull('deleted_at')->first();

        if (!isset($exist)):
            $user = new User();
        else:
            return GlobalFunction::sendSimpleResponse(false, __('Phone number taken.'));
        endif;

        if (isset($request->referral_code)) {
            $referrer = User::where('referral_code', $request->referral_code)
                ->when($this->brandSelector && isset($brand), function ($q) use ($brand) {
                    $q->where('brand', $brand);
                })->whereNull('deleted_at')->first();

            if (!isset($referrer)):
                return GlobalFunction::sendSimpleResponse(false, __('Invalid Referral Code.'));
            endif;
        }

        $user->name = $request->name;
        if ($this->brandSelector && isset($brand)) {
            $user->brand = $brand;
        }
        $user->email_address = $request->email_address ?: '-';
        $user->phone_number = $request->phone_number;
        // $user->race = $request->race;
        // $user->preferred_language = $request->preferred_language;
        // $user->hear_about = $request->hear_about;
        $user->dob = $request->dob ? Carbon::parse($request->dob)->format(config('app.db_date_format')) : null;
        $user->country = 'malaysia';
        $user->is_notification = 1;
        $user->is_active = 1;
        $user->is_verified = 1;
        $user->referral_code = Str::random(8);
        $user->save();

        // check vouchers or points if is referral
        $rewardsData = [];
        if (isset($request->referral_code) && isset($referrer)) {
            $vouchers = RedemptionVouchers::whereNull('deleted_at')->where('is_active', 1)->where('first_time_purchase', 1)->get();

            if ($vouchers->count() > 0) {
                foreach ($vouchers as $voucher) {
                    $rewardsData[] = [
                        'name' => $voucher->name,
                    ];

                    UserVouchers::create([
                        'user_id' => $user->id,
                        'voucher_id' => $voucher->id,
                        'voucher_code' => $voucher->code,
                        'effective_start_date' => $voucher->available_start_date,
                        'effective_end_date' => $voucher->available_end_date,
                        'voucher_data' => json_encode($voucher),
                    ]);
                }
            }

            $this->storeUpdateReferral($referrer->id, $user->id, $rewardsData);
        }

        Auth::login($user);
        $token_input = Str::random(60);
        $user->tokens()->delete();
        $user->token = $user->createToken($token_input)->plainTextToken;

        $success_msg = __('Account is created.');
        return GlobalFunction::sendDataResponse(true, $success_msg, $user);
    }

    function sendOtp(Request $request)
    {
        $brand = null;

        if ($this->brandSelector) {
            $brand = $request->route()->parameters['brand'];
        }

        $rules = [
            'phone_number' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);

        if ($result):
            return $result;
        endif;

        $otp = rand(100000, 999999);
        // $otp = '000000';
        Notification::route('whatsapp', $request->phone_number)
            ->notify(new AuthNotification($otp, $request->phone_number));

        $checkOtpExists = PhoneOtp::when($this->brandSelector && isset($brand), function ($q) use ($brand) {
            $q->where('brand', $brand);
        })
            ->where('phone_number', $request->phone_number)
            ->first();

        if (isset($checkOtpExists)):
            $checkOtpExists->delete();
        endif;

        PhoneOtp::create([
            'brand' => $brand,
            'phone_number' => $request->phone_number,
            'otp' => $otp
        ]);

        return GlobalFunction::sendSimpleResponse(true, __('OTP sent successfully. ' . $otp));
    }

    function login(Request $request)
    {
        $brand = null;

        if ($this->brandSelector) {
            $brand = $request->route()->parameters['brand'];
        }

        $rules = [
            'phone_number' => 'required',
            'otp' => 'required',
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if ($result):
            return $result;
        endif;

        $OtpCheck = PhoneOtp::when($this->brandSelector && isset($brand), function ($q) use ($brand) {
            $q->where('brand', $brand);
        })
            ->where('phone_number', $request->phone_number)
            ->where('otp', $request->otp)
            ->first();

        if (!$OtpCheck):
            return GlobalFunction::sendSimpleResponse(false, __('Invalid OTP.'));
        endif;

        $user = User::whereNull('deleted_at')
            ->where('is_active', 1)
            ->where('phone_number', $request->phone_number)
            ->when($this->brandSelector && isset($brand), function ($q) use ($brand) {
                $q->where('brand', $brand);
            })
            ->first();

        if (!$user):
            return GlobalFunction::sendDataResponse(true, __('Account does not exists.'), ['need_register' => 1]);
        else:
            $OtpCheck->delete();
        endif;

        $token_input = Str::random(60);
        $user->save();

        Auth::login($user);
        $user->tokens()->delete();
        $user->token = $user->createToken($token_input)->plainTextToken;
        $user = GlobalFunction::getUserData($user, $brand);

        return GlobalFunction::sendDataResponse(true, __('Account logged in.'), $user);
    }

    function forgotPassword(Request $request)
    {
        $rules = ['email_address' => 'required|email'];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if ($result):
            return $result;
        endif;

        $user = User::where('email_address', $request->email_address)
            ->where('is_active', 1)
            ->where('is_verified', 1)
            ->whereNull('deleted_at')
            ->first();
        if (!$user):
            return GlobalFunction::sendSimpleResponse(false, 'Account does not exists.');
        endif;

        $email_data = [
            'name' => $user->first_name . ' ' . $user->last_name,
            'email' => $user->email_address,
            'type' => 'member_forgot_password',
            'type_id' => $user->id,
        ];
        $this->sendVerificationEmail($email_data);

        $data = [
            'id' => $user->id,
            'email_address' => $user->email_address,
        ];
        return GlobalFunction::sendDataResponse(true, __('Please check your email for reset passsword.'), $data);
    }

    function resetPassword(Request $request)
    {
        $rules = [
            'email_address' => 'required|email',
            'password' => 'required',
            'confirm_password' => 'required|same:password',
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if ($result):
            return $result;
        endif;

        $user = User::where('email_address', $request->email_address)
            ->update([
                'password' => Hash::make($request->password),
                'is_first_time_login' => 0,
            ]);

        return GlobalFunction::sendSimpleResponse(true, __('Password is reset.'));
    }

    function codeVerification(Request $request)
    {
        $rules = [
            'type' => 'required',
            'id' => 'required',
            'code' => 'required',
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if ($result):
            return $result;
        endif;

        return $this->verifyCode($request);
    }

    function resendVerificationEmail(Request $request)
    {
        $type = $request->type;
        $type_data = $request->type_data;

        if (!$type || !$type_data):
            return GlobalFunction::sendSimpleResponse(false, 'Invalid request.');
        endif;

        if (in_array($type, ['member_signup', 'member_forgot_password', 'member_first_time_login'])):
            $user = User::where('email_address', $type_data)
                ->whereNull('deleted_at')
                ->first();

            if (!$user):
                return GlobalFunction::sendSimpleResponse(false, 'Account not found.');
            endif;

            if ($type == 'member_forgot_password' && $user->is_active == 0):
                return GlobalFunction::sendSimpleResponse(false, 'Account is disabled.');
            endif;

            if ($type == 'member_first_time_login' && $user->is_first_time_login == 0):
                return GlobalFunction::sendSimpleResponse(false, 'This account is logged in before.');
            endif;

            if (in_array($type, ['member_signup', 'member_first_time_login']) && $user->is_verified == 1):
                return GlobalFunction::sendSimpleResponse(false, 'Account is already verified.');
            endif;
        elseif (in_array($type, ['staff_forgot_password'])):
            $user = Doctors::where('email_address', $type_data)
                ->where('is_active', 1)
                ->whereNull('deleted_at')
                ->first();

            if (!$user):
                return GlobalFunction::sendSimpleResponse(false, 'Account not found.');
            endif;
        endif;

        $verify_data = [
            'name' => $user->first_name . ' ' . $user->last_name,
            'email' => $user->email_address,
            'type' => $type,
            'type_id' => $user->id,
        ];
        $this->sendVerificationEmail($verify_data);

        $data = [
            'id' => $user->id,
            'email_address' => $user->email_address,
        ];
        return GlobalFunction::sendDataResponse(true, __('Please check your email for verification.'), $data);
    }
}
