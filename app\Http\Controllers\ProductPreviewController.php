<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;

class ProductPreviewController extends Controller
{
    public function show(Request $request, $id)
    {
        $product = Product::findOrFail($id);

        $userAgent = strtolower($request->userAgent());

        $isSocialCrawler = str_contains($userAgent, 'whatsapp') ||
                           str_contains($userAgent, 'facebookexternalhit') ||
                           str_contains($userAgent, 'instagram') ||
                           str_contains($userAgent, 'linkedinbot') ||
                           str_contains($userAgent, 'twitterbot');

        // Return OG HTML for crawlers (so they can generate preview)
        if ($isSocialCrawler) {
            return response()->view('preview', ['product' => $product]);
        }

        // Redirect real users to your frontend site
        return redirect("https://miniprogram.mybolehboleh.com/product-detail/{$id}");
    }
    public function debug($id)
    {
        $product = Product::find($id);

        if (!$product) {
            return response('Product not found', 404);
        }

        // Optional: if you have a Blade file called preview.blade.php
        $html = view('preview', ['product' => $product])->render();

        // Extract <meta> tags (optional, for inspection)
        preg_match_all('/<meta[^>]+>/i', $html, $matches);

        return response()->json([
            'url' => url()->current(),
            'title' => $product->name,
            'image_url' => $product->image_url,
            'meta_tags' => [
                '<meta property="og:type" content="product">',
                '<meta property="og:title" content="'.$product->name.'">',
                '<meta property="og:image" content="'.$product->image_url.'">',
                '<meta property="og:site_name" content="MYBOLEHBOLEH">',
            ]
        ]);
    }

}

