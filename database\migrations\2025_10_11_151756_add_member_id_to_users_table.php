<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMemberIdToUsersTable extends Migration
{
    //Run the migrations
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('member_id', 20)->nullable()->unique()->after('id');
        });
    }

    // Reverse the migrations
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('member_id');
        });
    }
}
