<?php

namespace App\Http\Controllers\Api;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Carts;
use App\Models\Order;
use App\Models\Coupon;
use App\Models\ApiLogs;
use App\Models\Outlets;
use App\Models\Products;
use App\Models\ProductVariations;
use App\Models\Constants;
use App\Models\OrderItem;
use App\Traits\OrderTraits;
use App\Models\UserVouchers;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\UserAddressBook;
use App\Models\PushNotification;
use App\Services\LalamoveService;
use App\Models\BookingItemChanges;
use App\Models\RedemptionVouchers;
use Illuminate\Support\Facades\Log;
use App\Models\LalamoveStatusHistory;
use App\Traits\PaymentGatewaysTraits;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\LalamoveController;

class ApiOrders
{
    use OrderTraits;
    use PaymentGatewaysTraits;

    function listing(Request $request)
    {
        $per_page = $request->per_page ?? 10;

        $result = Order::where('user_id', auth()->user()->id)
            ->with('products')
            ->orderBy('id', 'desc')
            ->when($request->status, function ($q) use ($request) {
                return $q->whereIn('status', $request->status);
            }, function ($q) {
                return $q->whereNotIn('status', ['pending','pending_approval','approved','rejected']);
            })
            ->paginate($per_page);

        foreach ($result as $key => $item):
            $products = $this->getOrderProductDetails($item->orderItems);

            $data = [
                'id' => $item->id,
                'order_no' => $item->order_no,
                'delivery_method' => $item->delivery_method,
                'status' => config('staticdata.order_status')[$item->status] ?? null,
                'currency' => ($item->country == 'singapore') ? global_settings('currency_sg') : global_settings('currency'),
                'total' => formatNumber($item->total, 2),
                'created_at' => $item->created_at->format('Y-m-d H:i:s'),
                'outlet' => $item->outlet,
                'products' => $products,
            ];

            $result[$key] = $data;
        endforeach;

        return GlobalFunction::sendDataResponse(true, '', $result);
    }

    function getOrderProductDetails($order_item)
    {
        $normal_products = $order_item->filter(function ($product) {
            return !$product->order_bundle_id;
        })
            ->map(function ($product) {
                $data = [
                    'name' => $product->product_name,
                    'image' => $product->image,
                    'variation' => $product->variation_name,
                    'unit_price' => formatNumber((float) $product->price_per_quantity + (float) $product->unit_tax, 2),
                    'quantity' => $product->quantity,
                    'unit_weight' => $product->unit_weight,
                    'total_weight' => $product->total_weight,
                    'total_price' => formatNumber((float) $product->total + (float) $product->total_tax, 2),
                    'is_birthday' => $product->is_birthday ? true : false,
                    'is_voucher' => $product->voucher_id ? true : false,
                ];
                return $data;
            })
            ->whenEmpty(function () {
                return null;
            });

        $bundle_products = $order_item->filter(function ($product) {
            return $product->order_bundle_id;
        })
            ->groupBy('order_bundle_id')
            ->map(function ($bundle_item) {
                $data = [
                    'name' => $bundle_item->first()->order_bundle->bundle_name,
                    'price' => formatNumber($bundle_item->first()->order_bundle->bundle_price, 2),
                    'products' => $bundle_item->where('is_gwp', 0)
                        ->where('is_pwp', 0)
                        ->map(function ($product) {
                            $data = [
                                'name' => $product->product_name,
                                'image' => $product->image,
                                'variation' => $product->variation_name,
                                'quantity' => $product->quantity,
                            ];
                            return $data;
                        })
                        ->whenEmpty(function () {
                            return null;
                        }),
                    'gwp' => $bundle_item->where('is_gwp', 1)
                        ->map(function ($product) {
                            $data = [
                                'name' => $product->product_name,
                                'variation' => $product->variation_name,
                                'quantity' => $product->quantity,
                            ];
                            return $data;
                        })
                        ->values()
                        ->whenEmpty(function () {
                            return null;
                        }),
                    'pwp' => $bundle_item->where('is_pwp', 1)
                        ->map(function ($product) {
                            $data = [
                                'name' => $product->product_name,
                                'variation' => $product->variation_name,
                                'unit_price' => formatNumber((float) $product->price_per_quantity + (float) $product->unit_tax, 2),
                                'quantity' => $product->quantity,
                                'total_price' => formatNumber((float) $product->total + (float) $product->total_tax, 2),
                            ];
                            return $data;
                        })
                        ->values()
                        ->whenEmpty(function () {
                            return null;
                        }),
                ];
                return $data;
            })
            ->values()
            ->whenEmpty(function () {
                return null;
            });

        return [
            'normal' => $normal_products,
            'bundle' => $bundle_products,
        ];
    }

    function detail($id)
    {
        $order = Order::with('outlet')->find($id);

        $status_progress = [
            [
                'status' => 'ORDERED',
                'checked' => 1
            ],
            [
                'status' => 'PREPARING',
                'checked' => in_array($order->status, ['paid','shipped','completed']) ? 1 : 0
            ],
            [
                'status' => 'COLLECTION',
                'checked' => in_array($order->status, ['shipped','completed']) ? 1 : 0
            ],
            [
                'status' => 'COMPLETED',
                'checked' => in_array($order->status, ['completed']) ? 1 : 0
            ],
        ];

        $order->currency = ($order->country == 'singapore') ? global_settings('currency_sg') : global_settings('currency');
        $order->state = config('staticdata.states')[$order->state] ?? null;
        $order->address_country = config('staticdata.country')[$order->address_country] ?? null;
        $order->status = config('staticdata.order_status')[$order->status] ?? null;
        $order->created_date = $order->created_at->format('Y-m-d H:i:s');
        $order->payment_method = config('staticdata.payment_method')[$order->payment_method] ?? null;
        $order->payment_date = isset($order->payment) ? $order->payment->created_at->format('Y-m-d H:i:s') : null;
        $order->payment_ref_no = isset($order->payment) ? $order->payment->payment_ref_no : null;
        $order->show_msg = isset($order->payment) ? $order->payment->show_msg : null;
        $order->shipping_fee = formatNumber($order->shipping_fee, 2);
        $order->coupon = formatNumber($order->coupon, 2);
        $order->delivery_discount = is_string($order->delivery_discount) ? $order->delivery_discount : formatNumber($order->delivery_discount, 2);
        $order->voucher = formatNumber($order->voucher, 2);
        $order->subtotal = formatNumber($order->subtotal, 2);
        $order->total = formatNumber($order->total, 2);
        $order->total_tax = formatNumber($order->total_tax, 2);
        $order->products = $this->getOrderProductDetails($order->orderItems);
        $order->status_progress = $status_progress;

        $order->makeHidden([
            'user_id',
            'user_type',
            'brand',
            'country',
            'voucher_id',
            'name',
            'email_address',
            'phone_number',
            'user_address_book_id',
            'sales_channel_id',
            'sales_channel_name',
            'platform_from',
            'platform_from_id',
            'completed_at',
            'created_by',
            'platform_created_at',
            'payment',
            'updated_at',
            'created_at',
            'orderItems',
        ]);

        // for frontend show purpose
        if ($order->show_msg == 1):
            $order->payment->show_msg = 0;
            $order->payment->save();
        endif;
        // END for frontend show purpose

        return GlobalFunction::sendDataResponse(true, '', $order);
    }

    function orderPaymentDetail($order_no)
    {
        $order = Order::with('outlet', 'couponDetail')->where('order_no', $order_no)->first();

        $order->currency = ($order->country == 'singapore') ? global_settings('currency_sg') : global_settings('currency');
        $order->state = config('staticdata.states')[$order->state] ?? null;
        $order->address_country = config('staticdata.country')[$order->address_country] ?? null;
        $order->status = config('staticdata.order_status')[$order->status] ?? null;
        $order->created_date = $order->created_at->format('Y-m-d H:i:s');
        $order->payment_method = config('staticdata.payment_method')[$order->payment_method] ?? null;
        $order->payment_date = isset($order->payment) ? $order->payment->created_at->format('Y-m-d H:i:s') : null;
        $order->payment_ref_no = isset($order->payment) ? $order->payment->payment_ref_no : null;
        $order->shipping_fee = formatNumber($order->shipping_fee, 2);
        $order->voucher = formatNumber($order->voucher, 2);
        $order->coupon = formatNumber($order->coupon, 2);
        $order->delivery_discount = formatNumber($order->delivery_discount, 2);
        $order->subtotal = formatNumber($order->subtotal, 2);
        $order->total = formatNumber($order->total, 2);
        $order->total_tax = formatNumber($order->total_tax, 2);
        $order->products = $this->getOrderProductDetails($order->orderItems);
        $order->applied_vouchers = null;


        if ($order->coupon_id != null) {
            if ($order->couponDetail->type == 'fixed') {
                $order->couponDetail->coupon_value = global_settings('currency') . ' ' . $order->couponDetail->value;
            } else {
                $order->couponDetail->coupon_value = $order->couponDetail->value . '%';
            }
        }


        if ($order->voucher_id != null) {
            $applied_vouchers = UserVouchers::where('user_id', $order->user_id)
                ->whereIn('id', json_decode($order->voucher_id))
                ->get();
            $applied_vouchers->map(function ($item) {
                $item->voucher_data = json_decode($item->voucher_data);

                if ($item->voucher_data->type == 'discount' && $item->voucher_data->discount_type == 'fixed') {
                    $item->voucher_value = global_settings('currency') . " " . $item->voucher_data->value;
                } elseif ($item->voucher_data->type == 'discount' && $item->voucher_data->discount_type == 'percentage') {
                    $item->voucher_value = $item->voucher_data->value . '%';
                } else {
                    $item->voucher_value = '';
                }
            });

            $order->applied_vouchers = $applied_vouchers;
        }

        $user_vouchers = UserVouchers::where('user_id', $order->user_id)->whereNull('used_date')
                ->when(is_array($order->voucher_id), function ($query) use ($order) {
                    $query->whereNotIn('id', $order->voucher_id);
                })
                ->where('effective_start_date', '<', Carbon::now())
                ->where('effective_end_date', '>', Carbon::now())
                ->get();

        if ($user_vouchers->count() > 0):

            $user_vouchers->map(function ($item) use($order){
                $item->voucher_data = json_decode($item->voucher_data);
                $item->status = 'Active';

                if ($item->voucher_data->type == 'discount' && $item->voucher_data->discount_type == 'fixed') {
                    $item->voucher_value = global_settings('currency') . " " . $item->voucher_data->value;
                } elseif ($item->voucher_data->type == 'discount' && $item->voucher_data->discount_type == 'percentage') {
                    $item->voucher_value = $item->voucher_data->value . '%';
                } else {
                    $item->voucher_value = '';
                }

                $isEffective = false;
                $cannotUseReason = null;
                $start_date = $item->effective_start_date;
                $end_date = $item->effective_end_date;
                if ($item->used_date):
                    $item->status = 'used';
                    $cannotUseReason = __('Voucher has been used on :date', ['date' => Carbon::parse($item->used_date)->format('j M Y')]);
                elseif ($start_date && strtotime($start_date) > time()):
                    $item->status = 'pending';
                    $cannotUseReason = __('Voucher only can be used starting from :date.', ['date' => Carbon::parse($start_date)->format('j M Y')]);
                elseif ($end_date && strtotime($end_date) < time()):
                    $item->status = 'expired';
                    $cannotUseReason = __('Voucher is expired on :date.', ['date' => Carbon::parse($end_date)->format('j M Y')]);
                else:
                    $isEffective = true;
                endif;
                $item->is_effective = $isEffective;
                $item->cannot_use_reason = $cannotUseReason;
                $item->is_selected = in_array($item->id, json_decode($order->voucher_id??"[]")) ? true : false;

                unset($item->portal);
                unset($item->voucher_data->portal);
                unset($item->voucher_data->user_ids);
                unset($item->voucher_data->created_at);
                unset($item->voucher_data->updated_at);
                unset($item->voucher_data->deleted_at);
                unset($item->voucher_data->max_quantity);
                unset($item->voucher_data->redeem_points);
                unset($item->voucher_data->redeemed_quantity);
                unset($item->voucher_data->balance_quantity);
                unset($item->voucher_data->membership_tiers);
                unset($item->voucher_data->available_end_date);
                unset($item->voucher_data->available_start_date);
                unset($item->voucher_data->redemption_end_date);
                unset($item->voucher_data->redemption_start_date);
                unset($item->voucher_data->is_active);
                return $item;
            });
        endif;

        $order->avaiable_vouchers = $user_vouchers;
        $order->makeHidden([
            'user_type',
            'brand',
            'country',
            'user_address_book_id',
            'sales_channel_id',
            'sales_channel_name',
            'platform_from',
            'platform_from_id',
            'completed_at',
            'created_by',
            'platform_created_at',
            'payment',
            'coupon_detail',
            'updated_at',
            'created_at',
            'orderItems',
        ]);

        return GlobalFunction::sendDataResponse(true, '', $order);
    }

    function applyVoucher(Request $request)
    {
        $voucher_id = $request->voucher_id;
        $voucher = [ (int)$voucher_id];
        $order_id = $request->id;

        $order = Order::find($order_id);

        if (!$order):
            return GlobalFunction::sendDataResponse(false, 'Please enter valid amount.', '');
        endif;

        $response = [
            'voucher_id' => (int) $voucher_id,
            'sub_amount' => number_format($order->subtotal, 2)
        ];

        $user_voucher = UserVouchers::where('user_id', $order->user_id)
            ->with('voucher')
            ->where('id', $voucher_id)
            ->whereNull('used_date')
            ->first();

        if ($order->coupon_id != null):
            return GlobalFunction::sendSimpleResponse(false, 'Unable to apply voucher, coupon already applied.');
        endif;

        $user_voucher->voucher_data = json_decode($user_voucher->voucher_data);

        if (!$user_voucher):
            return GlobalFunction::sendSimpleResponse(false, 'Please enter valid voucher.');
        endif;

        $claimed_voucher = UserVouchers::where('user_id', $order->user_id)->where('voucher_id', $voucher_id)->whereNotNull('used_date')->count();
        if ($user_voucher->voucher_data->voucher_per_usage != null && $claimed_voucher >= $user_voucher->voucher_data->voucher_per_usage):
            return GlobalFunction::sendSimpleResponse(false, 'Voucher is fully redeemed per customer.');
        endif;

        if ($order->subtotal >= ($user_voucher->voucher_data->min_spend ?? 0)) {

            $order->voucher_id = $order->voucher_id ? array_merge($order->voucher_id, $voucher) : $voucher;

            if ($user_voucher->voucher_data->type == 'discount') {
                if ($user_voucher->voucher_data->discount_type == 'percentage') {
                    $order->voucher = number_format($order->subtotal * ($user_voucher->voucher_data->value / 100), 2);
                } else {
                    $order->voucher = number_format($user_voucher->voucher_data->value, 2);
                }
                $order->total = number_format($order->total - $order->voucher, 2);
            }elseif ($user_voucher->voucher_data->type == 'delivery') {
                if ($user_voucher->voucher_data->discount_type == 'percentage') {
                    $order->delivery_discount = number_format($order->subtotal * ($user_voucher->voucher_data->value / 100), 2);
                } else {
                    $order->delivery_discount = number_format($user_voucher->voucher_data->value, 2);
                }
                $order->total = number_format($order->total - $order->delivery_discount, 2);
            }
            $order->point_earned = $this->calculatePoints($order->total, $order->country);
            $order->save();

            if ($user_voucher->voucher_data->type == 'free_product') {
                foreach ($user_voucher->voucher_data->voucher_products as $free_product) {
                    $product = Products::find($free_product->product_id);

                    $order_item = new OrderItem();
                    $order_item->order_id = $order->id;
                    $order_item->order_bundle_id = null;
                    $order_item->product_id = $free_product->product_id;
                    $order_item->product_name = optional($product)->name ?? 'Free Product';
                    $order_item->product_sku = optional($product)->product_id ?? 'Free';
                    $order_item->variation_id =  $free_product->variation_id;
                    $order_item->voucher_id =  $user_voucher->id;
                    $order_item->is_gwp = 0;
                    $order_item->is_pwp = 0;
                    $order_item->is_birthday = 0;
                    $order_item->quantity = $free_product->quantity;
                    $order_item->save();
                }
            }

            return GlobalFunction::sendDataResponse(true, '', $response);
        } else {
            $spend_more = $user_voucher->voucher_data->min_spend - $order->subtotal;
            return GlobalFunction::sendDataResponse(false, 'Minimum spending not reached,please spend ' . number_format($spend_more, 2) . ' more.', $response);
        }
    }

    function removeVoucher(Request $request)
    {
        $order = Order::find($request->id);
        $order->voucher_id =  array_diff(json_decode($order->voucher_id), [$request->voucher_id]);
        if($order->voucher_id == []){
            $order->voucher_id = null;
        }

        $user_voucher = UserVouchers::where('user_id', $order->user_id)
        ->with('voucher')
        ->where('id', $request->voucher_id)
        ->first();

        $user_voucher->voucher_data = json_decode($user_voucher->voucher_data);

        if ($user_voucher->voucher_data->type == 'discount') {
            $order->total = $order->total + $order->voucher;
            $order->voucher = null;
        }elseif ($user_voucher->voucher_data->type == 'delivery') {
            $order->total = $order->total + $order->delivery_discount;
            $order->delivery_discount = null;
        }

        $order->point_earned = $this->calculatePoints($order->total, $order->country);
        $order->save();

        $user_voucher = UserVouchers::with('voucher')
            ->where('id', $request->voucher_id)
            ->whereNull('used_date')
            ->first();

        $voucher_data = json_decode($user_voucher->voucher_data);
        if ($voucher_data->type == 'free_product') {
            $order->orderItems()->where('voucher_id', $user_voucher->id)->delete();
        }

        return GlobalFunction::sendDataResponse(true, 'Removed voucher from order', $order);
    }

    function applyCoupon(Request $request)
    {
        $coupon_code = $request->coupon_code;
        $order_id = $request->id;

        // check coupon exists
        $coupon = Coupon::where('code', $coupon_code)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNotNull('start_datetime')
                    ->where('start_datetime', '<=', date('Y-m-d H:i:s'))
                    ->orWhereNull('start_datetime');

                $query->whereNotNull('expiry_datetime')
                    ->where('expiry_datetime', '>=', date('Y-m-d H:i:s'))
                    ->orWhereNull('expiry_datetime');
            })
            ->first();

        if (!$coupon) {
            return GlobalFunction::sendSimpleResponse(false, 'Invalid coupon code.');
        }
        // end check coupon exists

        $order = Order::find($order_id);

        // check if order exists
        if (!$order) {
            return GlobalFunction::sendSimpleResponse(false, 'Order not found.');
        } else {
            if ($order->voucher_id == '[]' || $order->voucher_id == null) {
            } else {
                return GlobalFunction::sendSimpleResponse(false, 'Unable to apply coupon, voucher already applied.');
            }
        }
        // end check if order exists

        // check coupon usage
        $checkUsage = Order::where('user_id', $order->user_id)->where('coupon_id', $coupon->id)->count();

        if (!is_null($coupon->limit_per_user)) {
            if ($checkUsage >= $coupon->limit_per_user) {
                return GlobalFunction::sendSimpleResponse(false, 'Coupon usage limit exceeded.');
            }
        }
        // end check coupon usage

        // check minimum amount
        if (!is_null($coupon->minimum_amount)) {
            if ($order->subtotal < $coupon->minimum_amount) {
                return GlobalFunction::sendSimpleResponse(false, 'Minimum amount not met.');
            }
        }
        // end check minimum amount

        // check product ids and category ids
        $product_ids = null;
        $categery_ids = null;
        $invalidProducts = [];

        if (!is_null($coupon->product_ids)) {
            $product_ids = json_decode($coupon->product_ids, true);
        }

        if (!is_null($coupon->category_ids)) {
            $categery_ids = json_decode($coupon->category_ids, true);
        }

        $order_items = OrderItem::where('order_id', $order->id)
            ->pluck('product_id');

        if (!is_null($product_ids)) {
            $invalidProducts = $order_items->diff($product_ids);
        }

        if (count($invalidProducts) > 0) {
            return GlobalFunction::sendSimpleResponse(false, 'Invalid product selected.');
        }

        if (!is_null($categery_ids)) {
            $invalidProducts = OrderItem::where('order_id', $order->id)
                ->whereHas('product.productCategory', function ($query) use ($categery_ids) {
                    $query->whereNotIn('id', $categery_ids);
                })->pluck('product_id');

            if (count($invalidProducts) > 0) {
                return GlobalFunction::sendSimpleResponse(false, 'Invalid product selected.');
            }
        }
        // end check product ids and category ids

        if ($coupon->type == 'fixed') {
            $order->coupon = number_format($coupon->value, 2);
            $order->total = number_format($order->total - $coupon->value, 2);
        } else if ($coupon->type == 'percentage') {
            $order->coupon = number_format($order->total * ($coupon->value / 100), 2);
            $order->total = number_format($order->total - $order->coupon, 2);
        }
        $order->coupon_id = $coupon->id;
        $order->point_earned = $this->calculatePoints($order->total, $order->country);
        $order->save();

        $response = [
            'coupon_id' => (int) $coupon->id,
            'sub_amount' => number_format($order->subtotal, 2)
        ];

        return GlobalFunction::sendDataResponse(true, '', $response);
    }

    function removeCoupon(Request $request)
    {

        $order = Order::find($request->id);

        if ($order && $order->coupon_id != $request->coupon_id) {
            return GlobalFunction::sendSimpleResponse(false, 'Unable to remove coupon.');
        }

        $order->coupon_id = null;
        $order->total = $order->total + $order->coupon;
        $order->coupon = null;
        $order->point_earned = $this->calculatePoints($order->total, $order->country);
        $order->save();

        return GlobalFunction::sendDataResponse(true, 'Removed coupon from order', $order);
    }

    function pay(Request $request)
    {

        $order = Order::with('payment.order')->find($request->id);
        $payment = $order->payment;
        if($request->remark){
            $order->update(['remark' => $request->remark]);
        }
        $payment->update([
            'amount' => $order->total,
            'payment_link' => 1
        ]);

        $data = [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'payment_url' => $this->getPaymentUrl($payment->gateway, $payment, $payment->status ?? ''),
        ];
        return GlobalFunction::sendDataResponse(true, __('Payment Link'), $data);

    }

    function create(Request $request)
    {
        $data = [];

        $delivery_method = $request->delivery_method ?? '';
        $delivery_vehicle = null;
        $outlet_id = $request->outlet_id ??null;

        if (!$delivery_method) {
            return GlobalFunction::sendSimpleResponse(false, 'Delivery method is required.');
        }

        if ($delivery_method != 'delivery') {
            if (!$outlet_id) {
                return GlobalFunction::sendSimpleResponse(false, 'Outlet is required.');
            }
        }

        $payment_method = $request->payment_method ?? '';
        if (!$payment_method) {
            return GlobalFunction::sendSimpleResponse(false, 'Payment method is required.');
        }

        try {
            $user = auth()->user();
            $tier = $user->membership_tier;
            // $country = $user->country;
            // $brand = $user->brand;
            // $currency = ($country == 'malaysia') ? global_settings('currency') : global_settings('currency_sg');
            $currency = global_settings('currency');

            $cart = Carts::where('user_id', $user->id)->first();
            $cart_id = $cart->id;

            if ($delivery_method == 'delivery') {
                $delivery_vehicle = $this->getDeliveryDetail($cart)['delivery_type'];
            }

            $cart_products = $cart->products;
            if (!$cart_products):
                return GlobalFunction::sendSimpleResponse(false, 'Cart is empty.');
            endif;

            // ⭐ VALIDATE STOCK AVAILABILITY BEFORE CREATING ORDER
            $stock_validation = $this->validateStockBeforeOrder($cart_products);
            if ($stock_validation['has_error']):
                return GlobalFunction::sendSimpleResponse(false, $stock_validation['message']);
            endif;

            $order_data = [
                'cart_id' => $cart_id,
                'tier' => $tier,
                // 'country' => $country,
                // 'brand' => $brand,
                'user_id' => $user->id,
                'user_type' => 'customer',
                'address_id' => $cart->address_id,
                'voucher_id' => $cart->voucher_list,
                'payment_method' => $payment_method,
                'remarks' => $request->remarks ?? '',
                'products' => $cart_products,
                'delivery_method' => $delivery_method,
                'delivery_vehicle' => $delivery_vehicle,
                'outlet_id' => $outlet_id,
                'coupon_id' => $cart->coupon_id,
                'quotation_id' => $cart->quotation_id
            ];
            $order_data = (object) $order_data;
            $order = $this->createOrder($order_data);

            if (isset($order['status']) && $order['status'] == false):
                return GlobalFunction::sendDataResponse(false, $order['message'], $order['data']);
            endif;

            $order_total = $order->total;
            if ($order_total > 0):
                // get payment link
                $payment_data = [
                    'user_id' => $user->id,
                    'gateway' => $payment_method,
                    'type' => 'order',
                    'type_ref_id' => $order->id,
                    'amount' => $order->total,
                    'status' => Constants::paymentStatus['pending'],
                    'payment_from' => $request->payment_from ?? 'web'
                ];
                $payment = $this->insertPaymentTransaction($payment_data);
                $payment->order = $order;
                $payment_url = $this->getPaymentUrl($payment_data['gateway'], $payment, $payment_data['status'] ?? '');
            endif;

            $data = [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'payment_url' => $payment_url ?? null,
            ];

            $responseData = [
                'status' => true,
                'message' => 'Order is placed.',
            ];

        } catch (\Exception $e) {
            Log::error($e->getMessage());

            $responseData = [
                'status' => false,
                'message' => 'Somthing went wrong. Please contact support.',
            ];
        }

        return GlobalFunction::sendDataResponse($responseData['status'], $responseData['message'], $data);
    }

    function rating(Request $request)
    {
        $order_id = $request->order_id ?? '';

        $order = Order::find($order_id);

        if (!$order):
            return GlobalFunction::sendSimpleResponse(false, 'Order not found.');
        endif;

        $order->rating = $request->rating ?? 0;
        $order->review = $request->review ?? '';
        $order->save();

        $order->makeHidden([
            'user_id',
            'user_type',
            'brand',
            'country',
            'voucher_id',
            'name',
            'email_address',
            'phone_number',
            'user_address_book_id',
            'sales_channel_id',
            'sales_channel_name',
            'platform_from',
            'platform_from_id',
            'completed_at',
            'created_by',
            'platform_created_at',
            'payment',
            'updated_at',
            'created_at',
            'orderItems',
        ]);

        return GlobalFunction::sendDataResponse(true, 'Rate successfully.', $order);
    }

    function lalamoveOrder($quotation_id, $order, $outlet_id, $address_id)
    {
        Log::info('[LALAMOVE] Starting new order process', [
            'quotation_id' => $quotation_id,
            'order_id' => $order->id ?? null,
            'outlet_id' => $outlet_id,
            'address_id' => $address_id,
        ]);

        try {
            $quotation_detail = (new LalamoveService)->getQuotationDetail($quotation_id)['data'];
            Log::info('[LALAMOVE] Retrieved quotation detail', [
                'serviceType' => $quotation_detail['serviceType'] ?? null,
            ]);

            $outlet = Outlets::find($outlet_id);
            $address = UserAddressBook::find($address_id);

            if (!$outlet || !$address) {
                Log::error('[LALAMOVE] Missing outlet or address record', [
                    'outlet_id' => $outlet_id,
                    'address_id' => $address_id,
                ]);
                return GlobalFunction::sendSimpleResponse(false, 'Outlet or address not found.');
            }

            $order_request = [
                "data" => [
                    "quotationId" => $quotation_detail['quotationId'],
                    "sender" => [
                        "stopId" => $quotation_detail['stops'][0]['stopId'],
                        "name" => $outlet->name,
                        "phone" => $outlet->contact_number,
                    ],
                    "recipients" => [[
                        "stopId" => $quotation_detail['stops'][1]['stopId'],
                        "name" => $address->recipient,
                        "phone" => $address->phone_number,
                    ]],
                    'isPODEnabled' => false,
                    'isRecipientSMSEnabled' => true,
                    'partner' => env('APP_NAME'),
                ]
            ];

            Log::info('[LALAMOVE] Sending order request to API', [
                'quotationId' => $quotation_detail['quotationId'],
                'order_id' => $order->id ?? null,
            ]);

            $lalamove_order = (new LalamoveService)->placeOrder($order_request);

            if (isset($lalamove_order['error'])) {
                Log::warning('[LALAMOVE] Order creation failed, attempting reorder', [
                    'quotation_id' => $quotation_id,
                    'order_id' => $order->id ?? null,
                ]);
                $this->lalamoveReorder($quotation_id, $order);
                return;
            }

            // Save order details
            $order->delivery_method = 'delivery';
            $order->delivery_vehicle = $quotation_detail['serviceType'];
            $order->tracking_number = $lalamove_order['data']['orderId'] ?? null;
            $order->lalamove_quotation_id = $lalamove_order['data']['quotationId'] ?? null;
            $order->lalamove_status = $lalamove_order['data']['status'] ?? null;
            $order->share_link = $lalamove_order['data']['shareLink'] ?? null;
            $order->save();

            Log::info('[LALAMOVE] Order successfully created', [
                'order_id' => $order->id,
                'tracking_number' => $order->tracking_number,
                'share_link' => $order->share_link,
            ]);

        } catch (\Exception $e) {
            Log::error('[LALAMOVE] Exception during order creation', [
                'quotation_id' => $quotation_id,
                'order_id' => $order->id ?? null,
                'error' => $e->getMessage(),
            ]);

            return GlobalFunction::sendSimpleResponse(false, 'Something went wrong. Please contact support.');
        }
    }

    function lalamoveReorder($quotation_id, $order)
    {
        Log::warning('[LALAMOVE] Initiating reorder process', [
            'old_quotation_id' => $quotation_id,
            'order_id' => $order->id ?? null,
        ]);

        try {
            $api_log = ApiLogs::where('type', 'lalamove')
                ->where('url', 'LIKE', '%/v3/quotations')
                ->where('response', 'LIKE', '%' . $quotation_id . '%')
                ->where('response_status', 1)
                ->first();

            if (!$api_log) {
                Log::error('[LALAMOVE] No matching quotation log found for reorder', [
                    'quotation_id' => $quotation_id,
                    'order_id' => $order->id ?? null,
                ]);
                return;
            }

            $request = json_decode($api_log->request, true) ?? null;
            if (!$request) {
                Log::error('[LALAMOVE] Failed to decode reorder request payload', [
                    'quotation_id' => $quotation_id,
                ]);
                return;
            }

            $lalamove = (new LalamoveService)->getQuotation($request);
            $shipping_fee = $lalamove['data']['priceBreakdown']['total'] ?? null;

            $order->update(['lalamove_quotation_id' => $lalamove['data']['quotationId'] ?? null]);

            $outlet_coordinates = $request['data']['stops'][0]['coordinates'];
            $customer_coordinates = $request['data']['stops'][1]['coordinates'];

            $outlet_id = Outlets::where('latitude', $outlet_coordinates['lat'])->first()->id ?? null;
            $address_id = UserAddressBook::where('latitude', $customer_coordinates['lat'])->first()->id ?? null;

            Log::info('[LALAMOVE] Reorder attempt details', [
                'new_quotation_id' => $lalamove['data']['quotationId'] ?? null,
                'order_id' => $order->id,
                'shipping_fee' => $shipping_fee,
            ]);

            if ($outlet_id && $address_id) {
                $this->lalamoveOrder($lalamove['data']['quotationId'], $order, $outlet_id, $address_id);
            } else {
                Log::error('[LALAMOVE] Failed to resolve outlet or address for reorder', [
                    'outlet_id' => $outlet_id,
                    'address_id' => $address_id,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('[LALAMOVE] Exception during reorder', [
                'quotation_id' => $quotation_id,
                'order_id' => $order->id ?? null,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Validate stock availability before creating order
     * Prevents users from ordering items that are out of stock
     */
    function validateStockBeforeOrder($cart_products)
    {
        $out_of_stock_items = [];

        foreach ($cart_products as $cart_item) {
            $product_name = $cart_item->product->name ?? 'Unknown Product';
            $requested_qty = $cart_item->quantity;

            // Check if product has variations
            if ($cart_item->variation_id) {
                // Check variation stock
                $variation = ProductVariations::find($cart_item->variation_id);

                if (!$variation) {
                    $out_of_stock_items[] = "{$product_name} (variation not found)";
                    continue;
                }

                if ($variation->is_out_of_stock == 1 || $variation->is_active == 0) {
                    $out_of_stock_items[] = "{$product_name} ({$variation->variation_name}) - Out of stock";
                    continue;
                }

                $available_qty = $variation->quantity ?? 0;

                if ($available_qty < $requested_qty) {
                    $out_of_stock_items[] = "{$product_name} ({$variation->variation_name}) - Only {$available_qty} available, you requested {$requested_qty}";
                }

            } else {
                // Check product stock (no variation)
                $product = Products::find($cart_item->product_id);

                if (!$product) {
                    $out_of_stock_items[] = "{$product_name} (product not found)";
                    continue;
                }

                if ($product->is_out_of_stock == 1 || $product->is_active == 0) {
                    $out_of_stock_items[] = "{$product_name} - Out of stock";
                    continue;
                }

                // Check if product has quantity field
                if (isset($product->quantity)) {
                    $available_qty = $product->quantity;

                    if ($available_qty < $requested_qty) {
                        $out_of_stock_items[] = "{$product_name} - Only {$available_qty} available, you requested {$requested_qty}";
                    }
                }
            }
        }

        if (!empty($out_of_stock_items)) {
            $message = "Some items in your cart are no longer available:\n" . implode("\n", $out_of_stock_items);

            return [
                'has_error' => true,
                'message' => $message,
                'items' => $out_of_stock_items
            ];
        }

        return [
            'has_error' => false,
            'message' => 'Stock validation passed',
            'items' => []
        ];
    }
}
