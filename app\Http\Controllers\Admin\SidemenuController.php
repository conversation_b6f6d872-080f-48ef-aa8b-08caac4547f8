<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Bookings;
use App\Models\Constants;
use App\Models\GlobalFunction;

class SidemenuController extends Controller
{
    function listing()
    {
        $is_retailer = is_retailer();

        $menu_list = [];
        $menu_list['main'] = [
            'dashboard' => [
                'class' => 'menu-dashboard',
                'icon' => 'fas fa-tachometer-alt',
                'name' => __('Dashboard'),
                'route' => route('index'),
                'permission' => ['loyalty_dashboard', 'members_dashboard']
            ],
            'users' => [
                'class' => 'menu-users',
                'icon' => 'fa fa-user',
                'name' => __('Users'),
                'route' => route('users'),
                'permission' => 'view_user'
            ],
            'users_address_book' => [
                'class' => 'menu-user-address-books',
                'icon' => 'fa fa-address-book',
                'name' => __('Address Book'),
                'route' => route('userAddressBook'),
                'permission' => 'view_user_address_books'
            ],
            'orders' => [
                'class' => 'menu-orders',
                'icon' => 'fa fa-file-alt',
                'name' => __('Orders'),
                'route' => route('order'),
                'permission' => 'view_orders'
            ],
            'order_ratings' => [
                'class' => 'menu-order-ratings',
                'icon' => 'fa fa-star',
                'name' => __('Order Ratings'),
                'route' => route('order-rating'),
                'permission' => 'view_order_ratings'
            ],
            // 'bookings' => [
            //     'class' => 'menu-bookings',
            //     'icon' => 'fas fa-calendar-check',
            //     'name' => __('Stringing Bookings'),
            //     'route' => 'bookings',
            //     'permission' => 'view_appointment',
            //     'submenu' => $this->bookingsSubmenu()
            // ],
            // 'reviews' => [
            //     'class' => 'menu-reviews',
            //     'icon' => 'fas fa-star',
            //     'name' => __('Reviews & Ratings'),
            //     'route' => route('reviews'),
            //     'permission' => 'view_review'
            // ],
            'purchase_receipts' => [
                'class' => 'menu-purchase-receipts',
                'icon' => 'fa fa-newspaper',
                'name' => __('Purchase Receipts'),
                'route' => route('purchaseReceipt'),
                'permission' => 'view_purchase_receipts'
            ]
        ];

        $menu_list['reports'] = [
            'point_transactions' => [
                'class' => 'menu-point-transactions',
                'icon' => 'fa fa-coins',
                'name' => __('Point Transactions'),
                'route' => route('point'),
                'permission' => 'view_point_transactions'
            ],
            // 'wallet_transactions' => [
            //     'class' => 'menu-wallet-transactions',
            //     'icon' => 'fa fa-wallet',
            //     'name' => __('Wallet Transactions'),
            //     'route' => route('wallet', ['users']),
            //     'permission' => 'view_wallet_transactions'
            // ],
            // 'point_redemptions' => [
            //     'class' => 'menu-point-redemptions',
            //     'icon' => 'fa fa-receipt',
            //     'name' => __('Point Redemptions'),
            //     'route' => route('pointRedemptions'),
            //     'permission' => 'view_point_redemptions'
            // ],
            'redeemed_vouchers' => [
                'class' => 'menu-redeemed-vouchers',
                'icon' => 'fa fa-ticket-alt',
                'name' => __('Redeemed Vouchers'),
                'route' => route('redeemed-vouchers'),
                'permission' => 'view_redeemed_vouchers'
            ],
            'retailer_stock_transaction' => [
                'class' => 'menu-retailer-stock-transaction',
                'icon' => 'fa fa-archive',
                'name' => !$is_retailer ? __('Retailer Stock Transactions') : __('Stock Transactions'),
                'route' => route('retailerStockTransaction'),
                'permission' => 'view_retailer_stock_transaction'
            ],
            'retailer_credit_transaction' => [
                'class' => 'menu-retailer-credit-transaction',
                'icon' => 'fa fa-money-check',
                'name' => __('Retailer Credit Transactions'),
                'route' => route('retailerCreditTransaction'),
                'permission' => 'view_retailer_credit_transaction'
            ],
        ];

        $menu_list['ecommerce'] = [
            'product-categories' => [
                'class' => 'menu-product-categories',
                'icon' => 'fa fa-box',
                'name' => __('Product Categories'),
                'route' => route('productCategory'),
                'permission' => 'view_product_category'
            ],
            'product-subcategories' => [
                'class' => 'menu-product-subcategories',
                'icon' => 'fa fa-box',
                'name' => __('Product Subcategories'),
                'route' => route('productSubcategory'),
                'permission' => 'view_product_subcategory'
            ],
            'products' => [
                'class' => 'menu-products',
                'icon' => 'fa fa-box',
                'name' => __('Products'),
                'route' => route('products'),
                'permission' => 'view_product'
            ],
            'product_bundle' => [
                'class' => 'menu-product-bundle',
                'icon' => 'fa fa-boxes',
                'name' => __('Product Bundles'),
                'route' => route('productBundle'),
                'permission' => 'view_product_bundle'
            ],
            'birthday_gifts' => [
                'class' => 'menu-birthday-gifts',
                'icon' => 'fa fa-gift',
                'name' => __('Birthday Gifts'),
                'route' => route('birthdayGift'),
                'permission' => 'view_birthday_gifts'
            ],
            'attributes' => [
                'class' => 'menu-attributes',
                'icon' => 'fa fa-list',
                'name' => __('Attributes'),
                'route' => route('attributes'),
                'permission' => 'view_attribute'
            ],
            'price_groups' => [
                'class' => 'menu-price-groups',
                'icon' => 'fa fa-layer-group',
                'name' => __('Price Groups'),
                'route' => route('priceGroups'),
                'permission' => 'view_price_group'
            ],
            'taxes' => [
                'class' => 'menu-taxes',
                'icon' => 'fa fa-percent',
                'name' => __('Taxes'),
                'route' => route('taxes'),
                'permission' => 'view_tax'
            ],
            'redemption_vouchers' => [
                'class' => 'menu-redemption-vouchers',
                'icon' => 'fa fa-tags',
                'name' => __('Redemption Vouchers'),
                'route' => route('redemptionVouchers'),
                'permission' => 'view_redemption_voucher'
            ],
            'shipping_fee' => [
                'class' => 'menu-shipping-fee',
                'icon' => 'fa fa-ship',
                'name' => __('Shipping Fee'),
                'route' => route('shippingFee'),
                'permission' => 'view_shipping_fee'
            ],
            'postcode_regions' => [
                'class' => 'menu-postcode-regions',
                'icon' => 'fa fa-map-marked-alt',
                'name' => __('Postcode Regions'),
                'route' => route('postcodeRegion'),
                'permission' => 'view_postcode_region'
            ],
            'retailer_product' => [
                'class' => 'menu-retailer-product',
                'icon' => 'fa fa-box',
                'name' => !$is_retailer ? __('Retailer Products') : __('Products'),
                'route' => route('retailerProduct'),
                'permission' => 'view_retailer_product'
            ],
            'coupons' => [
                'class' => 'menu-coupons',
                'icon' => 'fa fa-tags',
                'name' => __('Coupons'),
                'route' => route('coupon'),
                'permission' => 'view_coupon'
            ],
        ];

        $menu_list['setup'] = [
            'banners' => [
                'class' => 'menu-banners',
                'icon' => 'fa fa-images',
                'name' => __('Banners'),
                'route' => route('banner'),
                'permission' => 'view_banners'
            ],
            'membership_tiers' => [
                'class' => 'menu-membership-tiers',
                'icon' => 'fa fa-users-cog',
                'name' => __('Membership Tiers'),
                'route' => route('membershipTier'),
                'permission' => 'view_membership_tiers'
            ],
            'news-and-announcement' => [
                'class' => 'menu-news-and-announcement',
                'icon' => 'fa fa-newspaper',
                'name' => __('News & Announcement'),
                'route' => route('newsAndAnnouncement'),
                'permission' => 'view_banners'
            ],
            'sales-channels' => [
                'class' => 'menu-sales-channels',
                'icon' => 'fa fa-copyright',
                'name' => __('Sales Channels'),
                'route' => route('salesChannel'),
                'permission' => 'view_sales_channels'
            ],
            'ads_spending_transaction' => [
                'class' => 'menu-ads-spending-transaction',
                'icon' => 'fa fa-credit-card',
                'name' => __('Ads Spending Transaction'),
                'route' => route('adsSpendingTransaction'),
                'permission' => 'view_ads_spending_transaction'
            ],
            // 'string_types' => [
            //     'class' => 'menu-string-types',
            //     'icon' => 'fa fa-tenge',
            //     'name' => __('String Types'),
            //     'route' => route('stringType'),
            //     'permission' => 'view_string_types'
            // ],
            // 'string_tensions' => [
            //     'class' => 'menu-string-tensions',
            //     'icon' => 'fa fa-signal',
            //     'name' => __('String Tensions'),
            //     'route' => route('stringTension'),
            //     'permission' => 'view_string_tensions'
            // ],
            // 'string_colors' => [
            //     'class' => 'menu-string-colors',
            //     'icon' => 'fa fa-palette',
            //     'name' => __('String Colors'),
            //     'route' => route('stringColor'),
            //     'permission' => 'view_string_colors'
            // ],
            'chain_stores' => [
                'class' => 'menu-chain-stores',
                'icon' => 'fa fa-store',
                'name' => __('Chain Stores'),
                'route' => route('chainStore'),
                'permission' => 'view_chain_stores'
            ],
            'store' => [
                'class' => 'menu-stores',
                'icon' => 'fa fa-store',
                'name' => __('Store Locators'),
                'route' => route('store'),
                'permission' => 'view_store'
            ],
            // 'doctors' => [
            //     'class' => 'menu-doctors',
            //     'icon' => 'fa fa-user-friends',
            //     'name' => __('Staffs'),
            //     'route' => route('doctors'),
            //     'permission' => 'view_doctor'
            // ],
            'retailers' => [
                'class' => 'menu-retailers',
                'icon' => 'fa fa-users',
                'name' => __('Retailers'),
                'route' => route('retailers'),
                'permission' => 'view_retailers'
            ],
            'admin_users' => [
                'class' => 'menu-admin-users',
                'icon' => 'fa fa-users',
                'name' => __('Admin Users'),
                'route' => route('admins'),
                'permission' => 'view_admin'
            ],
            'roles_privileges' => [
                'class' => 'menu-roles-privileges',
                'icon' => 'fa fa-user-shield',
                'name' => __('Roles & Privileges'),
                'route' => route('rolesPrivileges'),
                'permission' => 'view_role'
            ],
            'settings' => [
                'class' => 'menu-settings',
                'icon' => 'fa fa-cog',
                'name' => __('Settings'),
                'route' => route('settings'),
                'permission' => ['general_setting', 'point_setting', '3rd_party_setting', 'reminder_setting', 'mobile_version_setting']
            ],
        ];

        $menu_list['pages'] = [
            'faq' => [
                'class' => 'menu-faqs',
                'icon' => 'fa fa-question-circle',
                'name' => __('FAQs'),
                'route' => route('faqs'),
                'permission' => 'view_faq'
            ],
            'privacy' => [
                'class' => 'menu-privacy',
                'icon' => 'fa fa-info',
                'name' => __('Privacy Policy'),
                'route' => route('pagesView', ['type' => 'privacy-policy']),
                'permission' => 'view_privacy'
            ],
            'terms' => [
                'class' => 'menu-terms',
                'icon' => 'fa fa-info',
                'name' => __('Terms and Conditions'),
                'route' => route('pagesView', ['type' => 'terms-of-use']),
                'permission' => 'view_term'
            ],
            'about' => [
                'class' => 'menu-about',
                'icon' => 'fa fa-info',
                'name' => __('About Us'),
                'route' => route('pagesView', ['type' => 'about-us']),
                'permission' => 'view_about'
            ],
        ];

        return $menu_list;
    }

    function validateMenu()
    {
        $menu_list = $this->listing();
        foreach ($menu_list as $main_key => $menu):
            foreach ($menu as $sub_key => $submenu):
                if (!is_array($submenu['permission'])):
                    if (!checkPermission($submenu['permission'])):
                        unset($menu_list[$main_key][$sub_key]);
                    endif;
                else:
                    $permission = $submenu['permission'];
                    $permission = array_filter($permission, function ($value) {
                        return checkPermission($value);
                    });

                    if (empty($permission)) {
                        unset($menu_list[$main_key][$sub_key]);
                    }
                endif;
            endforeach;

            if (empty($menu_list[$main_key])) {
                unset($menu_list[$main_key]);
            }
        endforeach;

        if (is_retailer()):
            $sections = ['reports', 'ecommerce', 'setup', 'pages'];
            foreach ($sections as $section):
                if (isset($menu_list[$section])):
                    $menu_list['main'] = array_merge($menu_list['main'], $menu_list[$section]);
                    unset($menu_list[$section]);
                endif;
            endforeach;
        else:
            unset($menu_list['ecommerce']['retailer_product']);
        endif;

        return $menu_list;
    }

    function bookingsSubmenu()
    {
        $type = [
            'all' => ['name' => __('All'), 'class' => 'badge-info'],
            'unpaid' => ['name' => __('unpaid'), 'class' => 'badge-secondary'],
            'paid' => ['name' => __('paid'), 'class' => 'badge-success'],
            'pending' => ['name' => __('pending'), 'class' => 'badge-warning'],
            'confirmed' => ['name' => __('confirmed'), 'class' => 'badge-warning'],
            'received_at_outlet' => ['name' => __('received_at_outlet'), 'class' => 'badge-success'],
            'ready_for_collect' => ['name' => __('ready_for_collect'), 'class' => 'badge-success'],
            'completed' => ['name' => __('completed'), 'class' => 'badge-success'],
            'cancelled' => ['name' => __('cancelled'), 'class' => 'badge-danger'],
        ];

        $count = $route = [];
        foreach ($type as $key => $label):
            $route[] = route('bookings', $key);
        endforeach;

        $bookings = Bookings::selectRaw('booking_status, count(*) as count')
            ->groupBy('booking_status')
            ->get();

        $bookings_all = 0;
        if ($bookings->count() > 0):
            foreach ($bookings as $row):
                $status_key = $row->booking_status;
                if (!isset($count[$status_key])):
                    $count[$status_key] = 0;
                endif;
                $count[$status_key] += $row->count;
                $bookings_all += $row->count;
            endforeach;
        endif;
        $count['all'] = $bookings_all;

        $payments = Bookings::selectRaw('payment_status, count(*) as count')
            ->groupBy('payment_status')
            ->get();
        if ($payments->count() > 0):
            foreach ($payments as $row):
                $status_key = $row->payment_status;
                if (!isset($count[$status_key])):
                    $count[$status_key] = 0;
                endif;
                $count[$status_key] += $row->count;
                $bookings_all += $row->count;
            endforeach;
        endif;

        return [
            'type' => $type,
            'count' => $count,
            'route' => $route
        ];
    }
}
