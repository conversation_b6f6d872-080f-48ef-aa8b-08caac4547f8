@extends('include.app')
@section('header')
    <style>
        .stats-card {
            border-radius: 4px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        .stats-total {
            background-color: #6777ef;
            color: white;
            border-color: #6777ef;
        }
        .stats-valid {
            background-color: #28a745;
            color: white;
            border-color: #28a745;
        }
        .stats-invalid {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        .stats-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .preview-table {
            font-size: 13px;
        }
        .preview-table th {
            background-color: #6777ef;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
            border-color: #5a67d8;
        }
        .row-valid {
            background-color: #d4edda;
        }
        .row-invalid {
            background-color: #f8d7da;
        }
        .editable-cell {
            cursor: pointer;
            position: relative;
        }
        .editable-cell:hover {
            background-color: #e9ecef;
        }
        .editable-cell input {
            width: 100%;
            border: 1px solid #007bff;
            padding: 4px;
        }
        .error-badge {
            display: inline-block;
            background-color: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-right: 4px;
        }
        .compare-value {
            font-size: 11px;
            color: #6c757d;
        }
        .compare-arrow {
            color: #007bff;
            font-weight: bold;
        }
        .table-wrapper {
            max-height: 600px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
    </style>
@endsection

@section('content')
<span class="d-none" id="current-menu" data-menu="menu-orders"></span>
<div class="card mb-4">
    <div class="card-header">
        <h4 class="flex-grow-1">
            <i class="fa fa-eye mr-2"></i>{{ __('Review Import Data') }}
            <div class="text-muted"><small>{{ __('Review and confirm order updates') }}</small></div>
        </h4>

        <div>
            <a href="{{ route('orderImportCancel') }}" class="btn btn-secondary text-white">
                <i class="fa fa-times"></i> {{ __('Cancel') }}
            </a>
        </div>
    </div>

    <div class="card-body">
                        <!-- Statistics -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="stats-card stats-total">
                                    <div class="stats-number">{{ $stats['total'] }}</div>
                                    <div>{{ __('Total Rows') }}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-card stats-valid">
                                    <div class="stats-number">{{ $stats['valid'] }}</div>
                                    <div>{{ __('Valid Rows') }}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-card stats-invalid">
                                    <div class="stats-number">{{ $stats['invalid'] }}</div>
                                    <div>{{ __('Invalid Rows') }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Filter Options -->
                        <div class="mb-3">
                            <label class="mr-3">
                                <input type="checkbox" id="filter-all" checked> {{ __('Show All') }}
                            </label>
                            <label class="mr-3">
                                <input type="checkbox" id="filter-valid" checked> {{ __('Show Valid') }}
                            </label>
                            <label class="mr-3">
                                <input type="checkbox" id="filter-invalid" checked> {{ __('Show Invalid') }}
                            </label>
                        </div>

                        <div class="alert alert-info">
                            <i class="fa fa-info-circle mr-2"></i>
                            {{ __('Click on any cell to edit. Only valid rows will be processed. Review the data below and click "Confirm & Update" to proceed.') }}
                        </div>

                        <!-- Preview Table -->
                        <div class="table-wrapper">
                            <table class="table table-bordered table-hover preview-table" id="preview-table">
                                <thead>
                                    <tr>
                                        <th width="50px">
                                            <input type="checkbox" id="select-all" checked>
                                        </th>
                                        <th width="50px">{{ __('Row') }}</th>
                                        <th width="80px">{{ __('Status') }}</th>
                                        <th>{{ __('Order Number') }}</th>
                                        <th>{{ __('Tracking Number') }}</th>
                                        <th>{{ __('Delivery Method') }}</th>
                                        <th>{{ __('Errors / Current Values') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($previewData as $index => $row)
                                        <tr class="data-row {{ $row['is_valid'] ? 'row-valid' : 'row-invalid' }}" 
                                            data-valid="{{ $row['is_valid'] ? '1' : '0' }}"
                                            data-index="{{ $index }}">
                                            <td class="text-center">
                                                <input type="checkbox" class="row-select" 
                                                       data-index="{{ $index }}"
                                                       {{ $row['is_valid'] ? 'checked' : '' }}
                                                       {{ !$row['is_valid'] ? 'disabled' : '' }}>
                                            </td>
                                            <td class="text-center">{{ $row['row_number'] }}</td>
                                            <td class="text-center">
                                                @if($row['is_valid'])
                                                    <span class="badge badge-success">
                                                        <i class="fa fa-check"></i> Valid
                                                    </span>
                                                @else
                                                    <span class="badge badge-danger">
                                                        <i class="fa fa-times"></i> Invalid
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="editable-cell" data-field="order_number" data-index="{{ $index }}">
                                                {{ $row['order_number'] }}
                                            </td>
                                            <td class="editable-cell" data-field="tracking_number" data-index="{{ $index }}">
                                                {{ $row['tracking_number'] }}
                                                @if($row['order_data'] && $row['order_data']['current_tracking_number'])
                                                    <div class="compare-value">
                                                        <span class="compare-arrow">←</span> Current: {{ $row['order_data']['current_tracking_number'] }}
                                                    </div>
                                                @endif
                                            </td>
                                            <td class="editable-cell" data-field="delivery_method" data-index="{{ $index }}">
                                                {{ $row['delivery_method'] }}
                                                @if($row['order_data'] && $row['order_data']['current_delivery_method'])
                                                    <div class="compare-value">
                                                        <span class="compare-arrow">←</span> Current: {{ $row['order_data']['current_delivery_method'] }}
                                                    </div>
                                                @endif
                                            </td>
                                            <td>
                                                @if(!empty($row['errors']))
                                                    @foreach($row['errors'] as $error)
                                                        <span class="error-badge">{{ $error }}</span>
                                                    @endforeach
                                                @else
                                                    <span class="text-success">{{ __('Ready to update') }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Action Buttons -->
                        <div class="text-center">
                            <button type="button" class="btn btn-primary btn-lg" id="confirm-btn">
                                <i class="fa fa-check mr-2"></i>{{ __('Confirm & Update Selected Rows') }}
                                <span id="selected-count">({{ $stats['valid'] }})</span>
                            </button>
                        </div>
                    </div>
</div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function() {
            let editedData = {};

            // Filter functionality
            function applyFilters() {
                const showAll = $('#filter-all').is(':checked');
                const showValid = $('#filter-valid').is(':checked');
                const showInvalid = $('#filter-invalid').is(':checked');

                $('.data-row').each(function() {
                    const isValid = $(this).data('valid') == '1';
                    let shouldShow = false;

                    if (showAll) {
                        shouldShow = true;
                    } else {
                        if (isValid && showValid) shouldShow = true;
                        if (!isValid && showInvalid) shouldShow = true;
                    }

                    $(this).toggle(shouldShow);
                });
            }

            $('#filter-all, #filter-valid, #filter-invalid').on('change', function() {
                if ($(this).attr('id') === 'filter-all' && $(this).is(':checked')) {
                    $('#filter-valid, #filter-invalid').prop('checked', false);
                }
                applyFilters();
            });

            // Select all functionality
            $('#select-all').on('change', function() {
                $('.row-select:enabled').prop('checked', $(this).is(':checked'));
                updateSelectedCount();
            });

            $('.row-select').on('change', function() {
                updateSelectedCount();
            });

            function updateSelectedCount() {
                const count = $('.row-select:checked').length;
                $('#selected-count').text(`(${count})`);
            }

            // Inline editing
            $('.editable-cell').on('click', function() {
                const cell = $(this);
                const field = cell.data('field');
                const index = cell.data('index');
                const currentValue = cell.text().trim().split('\n')[0].trim();

                if (cell.find('input').length > 0) return;

                const input = $('<input type="text" class="form-control form-control-sm">').val(currentValue);
                cell.html(input);
                input.focus();

                input.on('blur', function() {
                    const newValue = $(this).val();
                    
                    if (!editedData[index]) {
                        editedData[index] = {};
                    }
                    editedData[index][field] = newValue;

                    cell.text(newValue);
                });

                input.on('keypress', function(e) {
                    if (e.which === 13) {
                        $(this).blur();
                    }
                });
            });

            // Confirm button
            $('#confirm-btn').on('click', function() {
                const selectedRows = [];
                $('.row-select:checked').each(function() {
                    selectedRows.push($(this).data('index'));
                });

                if (selectedRows.length === 0) {
                    Swal.fire({
                        icon: 'warning',
                        title: '{{ __("No Rows Selected") }}',
                        text: '{{ __("Please select at least one valid row to update") }}'
                    });
                    return;
                }

                Swal.fire({
                    title: '{{ __("Confirm Update") }}',
                    text: `{{ __("Are you sure you want to update") }} ${selectedRows.length} {{ __("orders?") }}`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: '{{ __("Yes, Update") }}',
                    cancelButtonText: '{{ __("Cancel") }}'
                }).then((result) => {
                    if (result.isConfirmed) {
                        performUpdate(selectedRows);
                    }
                });
            });

            function performUpdate(selectedRows) {
                Swal.fire({
                    title: '{{ __("Updating...") }}',
                    text: '{{ __("Please wait while we update the orders") }}',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: '{{ route("orderImportConfirm") }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        selected_rows: selectedRows,
                        edited_data: editedData
                    },
                    success: function(response) {
                        if (response.status) {
                            Swal.fire({
                                icon: 'success',
                                title: '{{ __("Success") }}',
                                html: `
                                    <p>${response.message}</p>
                                    <p><strong>{{ __("Updated:") }}</strong> ${response.data.success_count}</p>
                                    ${response.data.failed_count > 0 ? `<p><strong>{{ __("Failed:") }}</strong> ${response.data.failed_count}</p>` : ''}
                                `,
                                confirmButtonText: '{{ __("Go to Orders") }}'
                            }).then(() => {
                                window.location.href = '{{ route("order") }}';
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: '{{ __("Error") }}',
                                text: response.message
                            });
                        }
                    },
                    error: function(xhr) {
                        let message = '{{ __("An error occurred while updating the orders") }}';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            icon: 'error',
                            title: '{{ __("Error") }}',
                            text: message
                        });
                    }
                });
            }

            updateSelectedCount();
        });
    </script>
@endsection
