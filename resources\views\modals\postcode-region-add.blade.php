<form action="{{ route('postcodeRegionAddUpdate') }}" method="post" enctype="multipart/form-data" id="{{ $moduleID }}Form" autocomplete="off">
    @csrf
    <input type="hidden" name="id" value="">
    
    <div class="row">
        @if (checkFeatureControl('selector', 'brand'))
        <div class="form-group col-md-12">
            <label>
                {{ __('Brand') }}
                <span class="text-danger">*</span>
            </label>
            <select name="brand[]" class="form-control select2" data-width="100%" data-placeholder="{{ __('Select Brand') }}" required multiple>
                @foreach (userBrandList() as $key => $brand)
                    <option value="{{ $key }}">{{ $brand }}</option>
                @endforeach
            </select>
        </div>
        @endif

        <div class="form-group col-md-12">
            <label>
                {{ __('Region Name') }}
                <span class="text-danger">*</span>
            </label>
            <input type="text" class="form-control" name="name" placeholder="{{ __('Enter region name') }}" required>
        </div>

        <div class="form-group col-md-12">
            <label>
                {{ __('Country') }}
                <span class="text-danger">*</span>
            </label>
            <select name="country" id="country_select" class="form-control select2" data-width="100%" required>
                <option value="">{{ __('Select Country') }}</option>
                @foreach(\App\Models\Country::orderBy('name')->get() as $country)
                    <option value="{{ $country->id }}">{{ $country->name }}</option>
                @endforeach
            </select>
        </div>

        <div class="col-md-12 form-group">
            <label>
                {{ __('Postcodes') }}
                <span class="text-danger">*</span>
            </label>
            <div class="table-responsive">
                <table id="postcode-table" class="table table-detail table-icon-css">
                    <thead>
                        <tr>
                            <th width="300px">{{ __('Postcode') }}</th>
                            <th width="80px" class="text-center">{{ __('Action') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="text" name="postcodes[]" class="form-control" placeholder="{{ __('Enter Postcode') }}" required>
                            </td>
                            <td class="text-center">
                                <button type="button" class="btn-icon btn-danger btn-remove-row"><i class="fa fa-trash-alt"></i></button>
                            </td>
                        </tr>
                        <tr hidden>
                            <td>
                                <input type="text" name="postcodes[new_row]" class="form-control" placeholder="{{ __('Enter Postcode') }}">
                            </td>
                            <td class="text-center">
                                <button type="button" class="btn-icon btn-danger btn-remove-row"><i class="fa fa-trash-alt"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="d-flex align-items-center">
                <div class="bulk-upload-container mr-3">
                    <input type="file" id="postcode-bulk-upload" accept=".xlsx,.xls,.xlsm,.xlsb,.xltx,.xltm,.csv" style="display: none;">
                    <button type="button" class="btn btn-sm btn-primary" id="btn-bulk-upload">
                        <i class="fa fa-upload"></i> {{ __('Bulk Upload Postcodes') }}
                    </button>
                </div>
                <a href="" class="btn-add-row" data-table="postcode-table"><i class="fa fa-plus"></i> {{ __('Add More Postcode') }}</a>
            </div>
        </div>

        <div class="form-group col-md-12">
            <label>{{ __('Active') }}</label>
            <div class="d-flex align-items-center">
                <label class="switch mb-0">
                    <input type="checkbox" name="is_active" id="is_active" value="1" class="enable" checked>
                    <span class="slider round"></span>
                </label>
                <label for="is_active" class="mb-0 ml-1 cursor-pointer">{{ __('Yes') }}</label>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Close') }}</button>
        <button type="submit" class="btn btn-primary btn-submit">{{ __('Save') }}</button>
    </div>
</form>

<script type="text/javascript">
    $(document).ready(function() {
        var moduleID = "{{ $moduleID }}";
        var formID = '#'+moduleID+'Form';

        // Bulk Upload Button Click Handler
        $('#btn-bulk-upload').on('click', function(e) {
            e.preventDefault();
            $('#postcode-bulk-upload').click();
        });

        // File Upload Handler
        $('#postcode-bulk-upload').on('change', function(e) {
            var file = e.target.files[0];
            if (!file) return;

            // Validate file type
            var fileName = file.name.toLowerCase();
            var validExtensions = ['.xlsx', '.xls', '.xlsm', '.xlsb', '.xltx', '.xltm', '.csv'];
            var isValidFile = validExtensions.some(function(ext) {
                return fileName.endsWith(ext);
            });
            
            if (!isValidFile) {
                swal('{{ __("Error") }}', '{{ __("Please upload a valid Excel or CSV file") }}', 'error');
                $(this).val('');
                return;
            }

            // Show loading
            swal({
                title: '{{ __("Processing...") }}',
                text: '{{ __("Reading file, please wait...") }}',
                icon: 'info',
                buttons: false,
                closeOnClickOutside: false,
                closeOnEsc: false
            });

            // Upload file via AJAX
            var formData = new FormData();
            formData.append('file', file);
            formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

            $.ajax({
                url: '{{ route("postcodeRegionBulkUpload") }}',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                timeout: 300000, // 5 minutes timeout for large files
                xhr: function() {
                    var xhr = new window.XMLHttpRequest();
                    // Upload progress
                    xhr.upload.addEventListener("progress", function(evt) {
                        if (evt.lengthComputable) {
                            var percentComplete = (evt.loaded / evt.total) * 100;
                            swal({
                                title: '{{ __("Uploading...") }}',
                                text: '{{ __("Progress") }}: ' + Math.round(percentComplete) + '%',
                                icon: 'info',
                                buttons: false,
                                closeOnClickOutside: false,
                                closeOnEsc: false
                            });
                        }
                    }, false);
                    return xhr;
                },
                success: function(response) {
                    swal.close();
                    
                    if (response.status && response.data && response.data.length > 0) {
                        // Show processing message for large datasets
                        if (response.data.length > 100) {
                            swal({
                                title: '{{ __("Processing...") }}',
                                text: '{{ __("Adding") }} ' + response.data.length + ' {{ __("postcodes to table") }}',
                                icon: 'info',
                                buttons: false,
                                closeOnClickOutside: false,
                                closeOnEsc: false
                            });
                        }
                        
                        // Clear existing postcodes (except hidden template)
                        $(formID+' #postcode-table tbody tr:not([hidden])').remove();
                        
                        // Add postcodes in batches to prevent UI freeze
                        var batchSize = 50;
                        var currentIndex = 0;
                        
                        function addBatch() {
                            var endIndex = Math.min(currentIndex + batchSize, response.data.length);
                            
                            for (var i = currentIndex; i < endIndex; i++) {
                                var postcode = response.data[i];
                                if (postcode && postcode.trim()) {
                                    var newRow = $(formID+' #postcode-table tbody tr[hidden]').clone();
                                    newRow.removeAttr('hidden');
                                    newRow.find('input[type="text"]').attr('name', 'postcodes[]').val(postcode.trim());
                                    $(formID+' #postcode-table tbody').append(newRow);
                                }
                            }
                            
                            currentIndex = endIndex;
                            
                            if (currentIndex < response.data.length) {
                                // Continue with next batch
                                setTimeout(addBatch, 10);
                            } else {
                                // All done
                                swal('{{ __("Success") }}', response.message || '{{ __("Postcodes uploaded successfully") }}', 'success');
                            }
                        }
                        
                        // Start adding batches
                        addBatch();
                    } else {
                        swal('{{ __("Error") }}', response.message || '{{ __("No valid postcodes found in the file") }}', 'error');
                    }
                    
                    // Clear file input
                    $('#postcode-bulk-upload').val('');
                },
                error: function(xhr, status, error) {
                    swal.close();
                    var errorMessage = '{{ __("An error occurred while uploading the file") }}';
                    
                    if (status === 'timeout') {
                        errorMessage = '{{ __("The file is too large or took too long to process. Please try a smaller file.") }}';
                    } else if (xhr.status === 413) {
                        errorMessage = '{{ __("The file is too large. Maximum file size is 50MB.") }}';
                    } else if (xhr.status === 500) {
                        errorMessage = '{{ __("Server error occurred while processing the file. The file might be too large or corrupted.") }}';
                    } else if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                errorMessage = response.message;
                            }
                        } catch (e) {
                            // Keep default error message
                        }
                    }
                    
                    console.error('Upload error:', status, error, xhr);
                    swal('{{ __("Error") }}', errorMessage, 'error');
                    $('#postcode-bulk-upload').val('');
                }
            });
        });

        // Unbind any previous handlers to prevent double submission
        $(formID).off('submit').on('submit', function(e) {
            e.preventDefault();
            e.stopImmediatePropagation();
            
            // Check if already submitting
            var $submitBtn = $(this).find('.btn-submit');
            if ($submitBtn.prop('disabled')) {
                return false;
            }
            
            var postcodes = [];
            var duplicatePostcode = false;
            var emptyPostcode = false;

            $(formID+' input[name="postcodes[]"]').each(function() {
                var postcode = $(this).val().trim();
                if (postcode) {
                    if (postcodes.includes(postcode)) {
                        duplicatePostcode = true;
                        return false;
                    }
                    postcodes.push(postcode);
                } else {
                    emptyPostcode = true;
                }
            });

            if (duplicatePostcode) {
                swal('{{ __("Error") }}', '{{ __("Duplicate postcode found") }}', 'error');
                return false;
            }

            if (postcodes.length === 0) {
                swal('{{ __("Error") }}', '{{ __("Please add at least one postcode") }}', 'error');
                return false;
            }

            var form = $(this);
            var formData = new FormData(this);
            
            // Disable submit button to prevent double submission
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> {{ __("Saving...") }}');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    var isSuccess = !!response.status;
                    var message = response.message;
                    if (Array.isArray(message)) { message = message.join('\n'); }

                    if(isSuccess) {
                        swal('{{ __('Success') }}', message || '{{ __('Saved') }}', 'success');
                        $(formID+'Modal').modal('hide');
                        $('#'+moduleID+'List table').DataTable().ajax.reload();
                        form[0].reset();
                        
                        // Reset postcodes table
                        $(formID+' #postcode-table tbody tr:not(:hidden)').remove();
                        var newRow = $(formID+' #postcode-table tbody tr:hidden').clone();
                        newRow.removeAttr('hidden');
                        newRow.find('input').attr('name', 'postcodes[]').val('');
                        $(formID+' #postcode-table tbody').prepend(newRow);
                    } else {
                        swal('{{ __('Error') }}', message || '{{ __('An error occurred') }}', 'error');
                    }
                },
                error: function(xhr) {
                    swal('{{ __('Error') }}', '{{ __('An error occurred') }}', 'error');
                },
                complete: function() {
                    // Re-enable submit button
                    $submitBtn.prop('disabled', false).html('{{ __("Save") }}');
                }
            });
        });
    });
</script>
