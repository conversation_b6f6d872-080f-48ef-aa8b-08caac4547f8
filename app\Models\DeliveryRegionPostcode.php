<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeliveryRegionPostcode extends Model
{
    use HasFactory;

    protected $table = 'delivery_region_postcode';

    protected $fillable = [
        'delivery_region_id',
        'postcode',
    ];

    /**
     * Get the region that owns the postcode.
     */
    public function region()
    {
        return $this->belongsTo(DeliveryRegion::class, 'delivery_region_id');
    }

    /**
     * Scope to find by postcode.
     */
    public function scopeByPostcode($query, $postcode)
    {
        return $query->where('postcode', trim($postcode));
    }

    /**
     * Scope to find by region.
     */
    public function scopeByRegion($query, $regionId)
    {
        return $query->where('delivery_region_id', $regionId);
    }
}
