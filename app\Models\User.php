<?php

namespace App\Models;

use App\Models\Order;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;

// use this
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'member_id',
        'name',
        'brand',
        'email_address',
        'password',
        'membership_tier',
        'dob',
        'race',
        'phone_number',
        'profile_image',
        'wallet_balance',
        'point_balance',
        'accumulated_points',
        'is_active',
        'is_first_time_login',
        'is_verified',
        'is_notification',
        'device_type',
        'device_token',
        'country',
        'preferred_language',
        'hear_about',
        'referral_code',
        'deleted_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'membershipHistories',
    ];

    public function age()
    {
        return Carbon::parse($this->attributes['dob'])->age;
    }

    public function membership_tiers()
    {
        return $this->belongsTo(MembershipTiers::class, 'membership_tier');
    }

    public function userAddressBooks()
    {
        return $this->hasMany(UserAddressBook::class);
    }

    public function vouchers()
    {
        return $this->hasMany(UserVouchers::class);
    }

    public function membershipHistories()
    {
        return $this->hasMany(UserMembership::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function purchaseReceipts()
    {
        return $this->hasMany(PurchaseReceipt::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointments::class, 'user_id', 'id');
    }

    public function retailer()
    {
        return $this->belongsTo(Retailer::class, 'retailer_id');
    }

    /**
     * Mutator for email_address to set default value "-" when empty
     */
    public function setEmailAddressAttribute($value)
    {
        $this->attributes['email_address'] = empty($value) ? '-' : $value;
    }

    public static function generateMemberId()
    {
        // Get the latest member_id
        $latestUser = self::whereNotNull('member_id')
            ->orderBy('member_id', 'desc')
            ->first();

        if (!$latestUser || !$latestUser->member_id) {
            return 'MB000001';
        }

        $latestNumber = (int) substr($latestUser->member_id, 2);
        $nextNumber = $latestNumber + 1;

        // Format with leading zeros (6 digits)
        return 'MB' . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }
}
