<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeliveryRegion extends Model
{
    use HasFactory;

    protected $table = 'delivery_region';

    protected $fillable = [
        'name',
        'country_id',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the country that owns the region.
     */
    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    /**
     * Get all postcodes for this region.
     */
    public function postcodes()
    {
        return $this->hasMany(DeliveryRegionPostcode::class, 'delivery_region_id');
    }

    /**
     * Get all products assigned to this region.
     */
    public function products()
    {
        return $this->belongsToMany(
            Products::class,
            'product_delivery_region',
            'delivery_region_id',
            'product_id'
        );
    }

    /**
     * Get country name attribute.
     */
    public function getCountryNameAttribute()
    {
        return $this->country ? $this->country->name : '';
    }

    /**
     * Get postcodes as array.
     */
    public function getPostcodesArrayAttribute()
    {
        return $this->postcodes()->pluck('postcode')->toArray();
    }

    /**
     * Get postcodes as comma-separated string.
     */
    public function getPostcodesListAttribute()
    {
        return $this->postcodes->pluck('postcode')->implode(', ');
    }

    /**
     * Check if a postcode exists in this region.
     */
    public function hasPostcode($postcode)
    {
        return $this->postcodes()
            ->where('postcode', trim($postcode))
            ->exists();
    }

    /**
     * Scope to get active regions only.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }

    /**
     * Scope to filter by country.
     */
    public function scopeByCountry($query, $countryId)
    {
        return $query->where('country_id', $countryId);
    }
}
