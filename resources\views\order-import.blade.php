@extends('include.app')
@section('header')
    <style>
        .upload-area {
            border: 2px dashed #6777ef;
            border-radius: 4px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fc;
            cursor: pointer;
            transition: all 0.3s;
        }
        .upload-area:hover {
            border-color: #5a67d8;
            background-color: #eef1f9;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #e8f5e9;
        }
        .upload-icon {
            font-size: 48px;
            color: #6777ef;
            margin-bottom: 15px;
        }
        #browse-btn {
            pointer-events: auto;
            z-index: 10;
            position: relative;
        }
        .file-preview-card {
            display: none;
            border: 2px solid #28a745;
            border-radius: 4px;
            background-color: #f8fff9;
            padding: 20px;
            margin-top: 20px;
        }
        .file-preview-card.show {
            display: block;
        }
        .file-icon {
            font-size: 48px;
            color: #28a745;
        }
        .file-info {
            flex-grow: 1;
        }
        .file-name {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .file-details {
            font-size: 13px;
            color: #6c757d;
        }
        .file-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .remove-file-btn {
            color: #dc3545;
            cursor: pointer;
            font-size: 24px;
            padding: 5px;
        }
        .remove-file-btn:hover {
            color: #a71d2a;
        }
    </style>
@endsection

@section('content')
<span class="d-none" id="current-menu" data-menu="menu-orders"></span>
<div class="card mb-4">
    <div class="card-header">
        <h4 class="flex-grow-1">
            <i class="fa fa-file-upload mr-2"></i>{{ __('Import Order Shipping Info') }}
            <div class="text-muted"><small>{{ __('Upload CSV file to update orders') }}</small></div>
        </h4>
    </div>

    <div class="card-body">
                        <!-- Download Template Button -->
                        <div class="text-center mb-4">
                            <a href="{{ route('orderImportTemplate') }}" class="btn btn-success btn-lg">
                                <i class="fa fa-download mr-2"></i>{{ __('Download CSV Template') }}
                            </a>
                        </div>

                        <hr>

                        <!-- Upload Area -->
                        <form id="upload-form" enctype="multipart/form-data">
                            @csrf
                            <div class="upload-area" id="upload-area">
                                <div class="upload-icon">
                                    <i class="fa fa-cloud-upload-alt"></i>
                                </div>
                                <h4>{{ __('Drag and drop your CSV file here') }}</h4>
                                <p class="text-muted">{{ __('or') }}</p>
                                <input type="file" name="file" id="file-input" accept=".csv,.xlsx,.xls" style="display: none;">
                                <button type="button" class="btn btn-primary" id="browse-btn" onclick="document.getElementById('file-input').click();">
                                    <i class="fa fa-folder-open mr-2"></i>{{ __('Browse Files') }}
                                </button>
                            </div>

                            <!-- File Preview Card -->
                            <div class="file-preview-card" id="file-preview-card">
                                <div class="d-flex align-items-center">
                                    <div class="file-icon mr-3">
                                        <i class="fa fa-file-excel"></i>
                                    </div>
                                    <div class="file-info">
                                        <div class="file-name" id="display-file-name">filename.csv</div>
                                        <div class="file-details">
                                            <span id="file-size">0 KB</span> • 
                                            <span id="file-type">CSV</span> • 
                                            <span class="text-success"><i class="fa fa-check-circle"></i> {{ __('Ready to upload') }}</span>
                                        </div>
                                    </div>
                                    <div class="file-actions">
                                        <button type="button" class="btn btn-danger" id="remove-file-btn" title="{{ __('Remove file') }}">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-success btn-lg" id="upload-btn" style="display: none;">
                                    <i class="fa fa-upload mr-2"></i>{{ __('Upload and Preview') }}
                                </button>
                            </div>
                        </form>
                    </div>
</div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function() {
            const uploadArea = $('#upload-area');
            const fileInput = $('#file-input');
            const browseBtn = $('#browse-btn');
            const fileName = $('#file-name');
            const uploadBtn = $('#upload-btn');
            const uploadForm = $('#upload-form');

            console.log('Upload area initialized:', uploadArea.length);
            console.log('File input initialized:', fileInput.length);
            console.log('Browse button initialized:', browseBtn.length);

            // Browse button click (backup - inline onclick is primary)
            browseBtn.on('click', function(e) {
                console.log('Browse button jQuery click event fired');
                // The onclick attribute will handle the actual click
            });

            // Upload area click (excluding the button)
            uploadArea.on('click', function(e) {
                // Don't trigger if clicking the browse button
                if ($(e.target).closest('#browse-btn').length === 0) {
                    console.log('Upload area clicked');
                    fileInput[0].click(); // Use native DOM click
                }
            });

            // File selected
            fileInput.on('change', function() {
                console.log('File input changed');
                const file = this.files[0];
                if (file) {
                    console.log('File selected:', file.name);
                    showFilePreview(file);
                } else {
                    console.log('No file selected');
                    hideFilePreview();
                }
            });

            // Show file preview card
            function showFilePreview(file) {
                const fileSize = (file.size / 1024).toFixed(2); // Convert to KB
                const fileType = file.name.split('.').pop().toUpperCase();
                
                $('#display-file-name').text(file.name);
                $('#file-size').text(fileSize + ' KB');
                $('#file-type').text(fileType);
                
                // Hide upload area and show preview card
                uploadArea.slideUp(300, function() {
                    $('#file-preview-card').addClass('show').hide().slideDown(300);
                    uploadBtn.fadeIn(300);
                });
            }

            // Hide file preview card
            function hideFilePreview() {
                $('#file-preview-card').slideUp(300, function() {
                    $(this).removeClass('show');
                    uploadArea.slideDown(300);
                    uploadBtn.fadeOut(300);
                });
                fileInput.val(''); // Clear file input
            }

            // Remove file button
            $('#remove-file-btn').on('click', function() {
                hideFilePreview();
            });

            // Drag and drop
            uploadArea.on('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('dragover');
            });

            uploadArea.on('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');
            });

            uploadArea.on('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');

                const files = e.originalEvent.dataTransfer.files;
                if (files.length) {
                    fileInput[0].files = files;
                    showFilePreview(files[0]);
                }
            });

            // Form submission
            uploadForm.on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);

                // Show loading
                Swal.fire({
                    title: '{{ __("Processing...") }}',
                    text: '{{ __("Uploading and validating your file") }}',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: '{{ route("orderImportUpload") }}',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status) {
                            Swal.fire({
                                icon: 'success',
                                title: '{{ __("Success") }}',
                                text: response.message,
                                confirmButtonText: '{{ __("Review Data") }}'
                            }).then(() => {
                                window.location.href = '{{ route("orderImportPreview") }}';
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: '{{ __("Error") }}',
                                text: response.message
                            });
                        }
                    },
                    error: function(xhr) {
                        let message = '{{ __("An error occurred while processing the file") }}';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            icon: 'error',
                            title: '{{ __("Error") }}',
                            text: message
                        });
                    }
                });
            });
        });
    </script>
@endsection
