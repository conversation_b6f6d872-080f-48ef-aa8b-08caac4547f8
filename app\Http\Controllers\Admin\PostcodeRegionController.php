<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Exports\GeneralExport;
use App\Imports\PostcodeImport;
use App\Models\GlobalFunction;
use App\Models\DeliveryRegion;
use App\Models\DeliveryRegionPostcode;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Validator;

class PostcodeRegionController extends Controller
{
    function __construct()
    {
        $this->brandSelector = checkFeatureControl('selector', 'brand');
    }

    function index()
    {
        if (!checkPermission('view_postcode_region')):
            return redirect(route('index'))->with('error', __('You do not have permission to view postcode regions.'));
        endif;

        $bulk_action = [
            'delete' => [
                'text' => __('Delete Selected'),
                'url' => route('postcodeRegionDelete')
            ],
            'export' => [
                'text' => __('Export Selected'),
                'url' => route('postcodeRegionExport'),
                'filename' => 'postcode_regions'
            ]
        ];

        if (!checkPermission('delete_postcode_region')):
            unset($bulk_action['delete']);
        endif;
        if (!checkPermission('export_postcode_region')):
            unset($bulk_action['export']);
        endif;

        $moduleID = 'postcode_regions';
        return view('postcode-region', compact('bulk_action', 'moduleID'));
    }

    function listing(Request $request)
    {
        $sort_col = $request->sort_col ?? 'id';
        $sort_by = $request->sort_by ?? 'DESC';
        
        $result = DeliveryRegion::with(['postcodes', 'country'])->when($request->status, function ($q) use ($request) {
            $q->where('is_active', $request->status);
        })
        ->orderBy($sort_col, $sort_by);
        
        $totalData = count($result->get());

        $data = [];
        if ($totalData > 0):
            $limit = $request->input('length');
            $start = $request->input('start');
            $result = $result->offset($start)
                ->limit($limit)
                ->get();

            foreach ($result as $item):
                $checkbox = '<label class="custom-checkbox">
                    <input type="checkbox" name="check[]" value="' . $item->id . '" class="check-selected form-check-input" data-datatable="postcode_regionsList">
                    <span></span>
                </label>';

                $status = '<div class="text-center">
                    <label class="switch mb-0">
                        <input type="checkbox" value="1" class="status-update" data-id="' . $item->id . '" ' . ($item->is_active == 1 ? 'checked' : '') . '>
                        <span class="slider round"></span>
                    </label>
                </div>';

                $action = '<div class="d-flex flex-wrap justify-content-center">';
                if (checkPermission('edit_postcode_region')):
                    $action .= '<a href="" class="btn-icon btn-info edit mr-1" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Edit') . '"><i class="fa fa-edit"></i></a>';
                endif;
                if (checkPermission('delete_postcode_region')):
                    $action .= '<a href="" class="btn-icon btn-danger delete" rel="' . $item->id . '" data-toggle="tooltip" data-title="' . __('Delete') . '"><i class="fa fa-trash-alt"></i></a>';
                endif;
                $action .= '</div>';

                $location = $item->country_name;
                
                $postcodes = $item->postcodes_list;
                $postcodes_display = strlen($postcodes) > 50 ? substr($postcodes, 0, 50) . '...' : $postcodes;
                
                $products_count = $item->products()->count();
                $products_badge = $products_count > 0 ? '<span class="badge badge-info">' . $products_count . ' products</span>' : '<span class="badge badge-secondary">No products</span>';

                $param = [
                    $checkbox,
                    $item->name,
                    $location,
                    $postcodes_display,
                    $products_badge,
                    $status,
                    Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
                    $action
                ];

                if ($request->checkbox == 0):
                    unset($param[0]);
                endif;
                if (!checkPermission('edit_postcode_region')):
                    unset($param[3]);
                    unset($param[5]);
                endif;

                $param = array_values($param);
                $data[] = $param;
            endforeach;
        endif;

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalData),
            "data"            => $data
        ];

        echo json_encode($json_data);
        exit();
    }

    function detail(Request $request)
    {
        $id = $request->id;
        $result = DeliveryRegion::with('postcodes')->find($id);

        if (!$result):
            return GlobalFunction::sendSimpleResponse(false, __('Postcode region not found.'));
        endif;

        // Format response for the form - return only needed fields
        $data = [
            'id' => $result->id,
            'name' => $result->name,
            'country' => $result->country_id,
            'country_id' => $result->country_id,
            'is_active' => $result->is_active,
            'postcodes' => $result->postcodes->pluck('postcode')->toArray()
        ];

        return GlobalFunction::sendDataResponse(true, '', $data);
    }

    function addUpdate(Request $request)
    {
        $id = $request->id ?? null;
        
        // Remove the template row (new_row) from postcodes before validation
        $postcodes = $request->input('postcodes', []);
        if (isset($postcodes['new_row'])) {
            unset($postcodes['new_row']);
        }
        
        // Filter out empty values and reindex array
        $postcodes = array_values(array_filter($postcodes, function($value) {
            return !empty(trim($value));
        }));
        
        // Replace the postcodes in the request
        $request->merge(['postcodes' => $postcodes]);
        
        $rules = [
            'name' => 'required|string|max:255',
            'country' => 'required',
            'postcodes' => 'required|array|min:1',
            'postcodes.*' => 'required|string',
        ];

        $validator = Validator::make($request->all(), $rules);
        
        if ($validator->fails()):
            return GlobalFunction::sendSimpleResponse(false, $validator->errors()->first());
        endif;

        if ($id):
            $msg = __('Updated!');
            $query = DeliveryRegion::find($id);
            if (!$query):
                return GlobalFunction::sendSimpleResponse(false, __('Postcode region not found.'));
            endif;
        else:
            $msg = __('Added!');
            $query = new DeliveryRegion();
        endif;

        // Clean and validate postcodes
        $postcodes = array_filter(array_map('trim', $request->postcodes), function($postcode) {
            return !empty($postcode);
        });

        \DB::beginTransaction();
        try {
            $query->name = $request->name;
            $query->country_id = $request->country;
            $query->is_active = $request->is_active ?? 0;

            $query->save();

            // Delete existing postcodes and recreate
            $query->postcodes()->delete();
            
            // Add new postcodes
            foreach (array_values($postcodes) as $postcode) {
                DeliveryRegionPostcode::create([
                    'delivery_region_id' => $query->id,
                    'postcode' => $postcode
                ]);
            }

            \DB::commit();
            return GlobalFunction::sendSimpleResponse(true, $msg);
        } catch (\Exception $e) {
            \DB::rollBack();
            return GlobalFunction::sendSimpleResponse(false, $e->getMessage());
        }
    }

    function statusUpdate(Request $request)
    {
        $id = $request->id;
        $query = DeliveryRegion::find($id);

        if (!$query):
            return GlobalFunction::sendSimpleResponse(false, __('Postcode region not found.'));
        endif;

        $query->is_active = $query->is_active == 1 ? 0 : 1;
        $query->save();

        return GlobalFunction::sendSimpleResponse(true, __('Updated!'));
    }

    function delete(Request $request)
    {
        $ids = $request->id;
        if (!is_array($ids)):
            $ids = [$ids];
        endif;

        foreach ($ids as $id):
            $query = DeliveryRegion::find($id);
            if ($query):
                $query->delete();
            endif;
        endforeach;

        return GlobalFunction::sendSimpleResponse(true, __('Deleted!'));
    }

    function export(Request $request)
    {
        $ids = $request->id;
        if (!is_array($ids)):
            $ids = [$ids];
        endif;

        $result = DeliveryRegion::with(['postcodes', 'country'])->whereIn('id', $ids)->get();

        $data = [];
        foreach ($result as $item):
            $data[] = [
                'Name' => $item->name,
                'Country' => $item->country_name,
                'Postcodes' => $item->postcodes_list,
                'Status' => $item->is_active ? 'Active' : 'Inactive',
                'Created At' => Carbon::parse($item->created_at)->format(config('app.display_datetime_format')),
            ];
        endforeach;

        return Excel::download(new GeneralExport($data), 'postcode_regions.xlsx');
    }

    function bulkUpload(Request $request)
    {
        // Increase time limit for large files
        set_time_limit(300); // 5 minutes
        ini_set('memory_limit', '512M'); // Increase memory limit
        
        // Log incoming request for debugging
        Log::info('Postcode upload request received', [
            'has_file' => $request->hasFile('file'),
            'file_name' => $request->hasFile('file') ? $request->file('file')->getClientOriginalName() : null,
            'file_extension' => $request->hasFile('file') ? $request->file('file')->getClientOriginalExtension() : null,
            'file_mime' => $request->hasFile('file') ? $request->file('file')->getMimeType() : null,
        ]);
        
        $validator = Validator::make($request->all(), [
            'file' => [
                'required',
                'file',
                'max:51200', // Max 50MB
                function ($attribute, $value, $fail) {
                    $allowedExtensions = ['xlsx', 'xls', 'xlsm', 'xlsb', 'xltx', 'xltm', 'csv'];
                    $extension = strtolower($value->getClientOriginalExtension());
                    
                    if (!in_array($extension, $allowedExtensions)) {
                        $fail('The file must be an Excel or CSV file.');
                    }
                    
                    // Additional MIME type check for common CSV and Excel types
                    $allowedMimeTypes = [
                        'text/csv',
                        'text/plain',
                        'application/csv',
                        'text/comma-separated-values',
                        'application/vnd.ms-excel',
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        'application/vnd.ms-excel.sheet.macroEnabled.12',
                        'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
                        'application/octet-stream'
                    ];
                    
                    $mimeType = $value->getMimeType();
                    
                    // For CSV files, be very lenient with MIME type
                    if ($extension === 'csv') {
                        return; // Accept any MIME type for .csv files
                    }
                    
                    if (!in_array($mimeType, $allowedMimeTypes)) {
                        $fail('The file type is not supported. Detected type: ' . $mimeType);
                    }
                },
            ],
        ]);

        if ($validator->fails()):
            Log::error('Validation failed', ['errors' => $validator->errors()->all()]);
            return GlobalFunction::sendSimpleResponse(false, $validator->errors()->first());
        endif;

        try {
            $file = $request->file('file');
            
            // Import the Excel file
            $import = new PostcodeImport();
            Excel::import($import, $file);
            
            // Get the postcodes
            $postcodes = $import->getPostcodes();
            
            if (empty($postcodes)):
                return GlobalFunction::sendSimpleResponse(false, __('No valid postcodes found in the Excel file. Please ensure the file contains postcode data in the first column.'));
            endif;

            $count = count($postcodes);
            $message = $count === 1 
                ? __('Successfully imported 1 postcode') 
                : __('Successfully imported :count postcodes', ['count' => $count]);
            
            return GlobalFunction::sendDataResponse(true, $message, $postcodes);
            
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            $errors = [];
            foreach ($failures as $failure) {
                $errors[] = "Row {$failure->row()}: " . implode(', ', $failure->errors());
            }
            return GlobalFunction::sendSimpleResponse(false, __('Validation errors: ') . implode('; ', $errors));
        } catch (\Exception $e) {
            Log::error('Postcode bulk upload error: ' . $e->getMessage(), [
                'file' => $file ? $file->getClientOriginalName() : 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return GlobalFunction::sendSimpleResponse(false, __('Error reading file: :error', ['error' => $e->getMessage()]));
        }
    }
}
