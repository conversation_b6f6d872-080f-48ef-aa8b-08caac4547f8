<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use App\Models\DeliveryRegion;
use App\Http\Controllers\Controller;

class ApiDeliveryRegion extends Controller
{
    function __construct(Request $request)
    {
        $this->brandSelector = checkFeatureControl('selector', 'brand');

        $this->brand = null;

        if ($this->brandSelector) {
            $this->brand = $request->route()->parameters['brand'] ?? null;
        }
    }

    function getCountries(Request $request)
    {
        $countries = DeliveryRegion::with('country')
            ->where('is_active', 1)
            ->get()
            ->pluck('country')
            ->unique('id')
            ->map(function ($country) {
                return [
                    'id' => $country->id,
                    'name' => $country->name
                ];
            })
            ->values();
        
        return GlobalFunction::sendDataResponse(true, '', $countries);
    }

    function getRegionsByCountry(Request $request, $countryId)
    {
        $regions = DeliveryRegion::where('country_id', $countryId)
            ->where('is_active', 1)
            ->get()
            ->map(function ($region) {
                return [
                    'id' => $region->id,
                    'name' => $region->name,
                    'products_count' => $region->products()->count()
                ];
            })
            ->values();
        
        if ($regions->isEmpty()) {
            return GlobalFunction::sendSimpleResponse(false, 'No regions found for this country');
        }
        
        return GlobalFunction::sendDataResponse(true, '', $regions);
    }

    function getAllRegions(Request $request)
    {
        $regions = DeliveryRegion::with('country')
            ->where('is_active', 1)
            ->get()
            ->map(function ($region) {
                return [
                    'id' => $region->id,
                    'name' => $region->name,
                    'country_id' => $region->country_id,
                    'country_name' => optional($region->country)->name,
                    'products_count' => $region->products()->count()
                ];
            })
            ->values();
        
        return GlobalFunction::sendDataResponse(true, '', $regions);
    }

    function getRegionPostcodes(Request $request, $id)
    {
        $region = DeliveryRegion::where('id', $id)
            ->where('is_active', 1)
            ->first();
        
        if (!$region) {
            return GlobalFunction::sendSimpleResponse(false, 'Region not found or inactive!');
        }
        
        $postcodes = $region->postcodes->pluck('postcode')->values()->all();
        
        return GlobalFunction::sendDataResponse(true, '', $postcodes);
    }
}

