@extends('include.app')
@section('header')
@endsection

@section('content')
<span class="d-none" id="current-menu" data-menu="menu-postcode-regions"></span>
<div class="card mb-4">
    <div class="card-header">
        <h4 class="flex-grow-1">
            {{ __('Postcode Regions') }}
        </h4>

        <div>
            @if(checkPermission('create_postcode_region'))
                <a id="{{ $moduleID }}Add" href="" class="ml-md-auto my-1 btn btn-primary text-white">{{ __('Add Postcode Region') }}</a>
            @endif
            @if(checkPermission('export_postcode_region'))
                <a href="" data-url="{{ route('postcodeRegionExport') }}" id="{{ $moduleID }}Export" class="my-1 ml-1 btn btn-secondary text-white">{{ __('Export') }}</a>
            @endif
        </div>
    </div>
    <div class="card-body table-id" id="{{ $moduleID }}List" data-module="{{ $moduleID }}">
        <div class="form-row text-left justify-content-end mb-2">
            <div class="col-md-3">
                <select name="fstatus" class="form-control select2 filter-form" data-width="100%">
                    <option value="">{{ __('All Statuses') }}</option>
                    <option value="1">{{ __('Active') }}</option>
                    <option value="0">{{ __('Inactive') }}</option>
                </select>
            </div>
            @if(checkFeatureControl('filter', 'brand'))
            <div class="col-md-3">
                <select name="fbrand" class="form-control select2 filter-form" data-width="100%">
                    <option value="">{{ __('All Brands') }}</option>
                    @foreach (userBrandList() as $key => $brand) 
                        <option value="{{ $key }}">{{ __($brand) }}</option>
                    @endforeach
                </select>
            </div>
            @endif
        </div>
        @if($bulk_action)
            @include('include.selected-box', [ 
                'bulk_action' => $bulk_action,
                'datatable_list' => $moduleID.'List'
            ])
        @endif
        <div class="table-responsive">
            <table class="table table-striped word-wrap" data-url="{{ route('postcodeRegionList') }}" data-delete="{{ route('postcodeRegionDelete') }}" data-detail="{{ route('postcodeRegionDetail') }}" data-status="{{ route('postcodeRegionStatusUpdate') }}">
                <thead>
                    <tr>
                        @if($bulk_action)
                        <th width="40px">
                            <label class="custom-checkbox">
                                <input type="checkbox" value="1" class="form-check-input checkall" data-datatable="{{ $moduleID }}List">
                                <span></span>
                            </label>
                        </th>
                        @endif
                        <th style="min-width:200px" data-column="name" class="sortable">{{ __('Region Name') }}</th>
                        <th style="min-width:200px">{{ __('Location') }}</th>
                        <th style="min-width:300px">{{ __('Postcodes') }}</th>
                        <th style="min-width:120px" class="text-center">{{ __('Assigned Products') }}</th>
                        @if(checkPermission('edit_postcode_region'))
                        <th style="min-width:100px" class="text-center">{{ __('Status') }}</th>
                        @endif
                        <th style="min-width:120px" data-column="created_at" class="sortable">{{ __('Created On') }}</th>
                        @if(checkPermission('edit_postcode_region') || checkPermission('delete_postcode_region'))
                        <th style="min-width:100px" class="text-center">{{ __('Action') }}</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <input type="hidden" name="sort_col" id="sort_col" value="{{ request()->sort_col }}">
        <input type="hidden" name="sort_by" id="sort_by" value="{{ request()->sort_by }}">
    </div>
</div>
@include('modals.layout', [
    'moduleID' => $moduleID,
    'filename' => 'postcode-region-add',
    'title' => __('Postcode Region'),
])
<script type="text/javascript">
    $(document).ready(function() {
        var moduleID = "{{ $moduleID }}";
        var listingID = '#'+moduleID+'List';
        var tableID = listingID+" table";
        var formID = '#'+moduleID+'Form';

        $('#'+moduleID+'Add').on("click", function (event) {
            event.preventDefault();
            $(formID)[0].reset();
            $(formID+' input[name="id"]').val('');
            $(formID+'Modal').modal('show');
            $(formID+'Modal #modalTitle #addTitle').removeClass('d-none');
            $(formID+'Modal #modalTitle #editTitle').addClass('d-none');
            
            @if(checkFeatureControl('selector', 'brand'))
            $(formID+' select[name="brand[]"]').val(null).trigger('change');
            @endif

            // Reset country
            $(formID+' select[name="country"]').val('').trigger('change');

            // Reset to one empty postcode row
            $(formID+' #postcode-table tbody tr:not([hidden])').remove();
            var newRow = $(formID+' #postcode-table tbody tr[hidden]').clone();
            newRow.removeAttr('hidden');
            newRow.find('input[type="text"]').attr('name', 'postcodes[]').val('');
            $(formID+' #postcode-table tbody').prepend(newRow);
        });

        $(tableID).on("click", ".edit", function (event) 
        {
            var id = $(this).attr("rel");
            event.preventDefault();

            $.ajax({
                url: "{{ route('postcodeRegionDetail') }}",
                type: "POST",
                data: {
                    id: id,
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                dataType: "json",
                success: function(response) {
                    if(response.status){
                        var data = response.data;
                        
                        $(formID+'Modal').modal('show');
                        $(formID+'Modal #modalTitle #editTitle').removeClass('d-none');
                        $(formID+'Modal #modalTitle #addTitle').addClass('d-none');
                        $(formID+' input[name="id"]').val(data.id);
                        $(formID+' input[name="name"]').val(data.name);
                        
                        @if(checkFeatureControl('selector', 'brand'))
                        if(data.brand) {
                            $(formID+' select[name="brand[]"]').val(data.brand).trigger('change');
                        }
                        @endif

                        // Set country
                        if(data.country) {
                            $(formID+' select[name="country"]').val(data.country).trigger('change');
                        }

                        $(formID+' input[name="is_active"]').prop('checked', data.is_active == 1);

                        // Clear existing postcodes (except hidden template)
                        $(formID+' #postcode-table tbody tr:not([hidden])').remove();

                        // Add postcodes
                        if(data.postcodes && data.postcodes.length > 0) {
                            data.postcodes.forEach(function(postcode) {
                                var newRow = $(formID+' #postcode-table tbody tr[hidden]').clone();
                                newRow.removeAttr('hidden');
                                newRow.find('input[type="text"]').attr('name', 'postcodes[]').val(postcode);
                                $(formID+' #postcode-table tbody').append(newRow);
                            });
                        }
                    } else {
                        swal('{{ __('Error') }}', response.message || '{{ __('Failed to load data') }}', 'error');
                    }
                },
                error: function(xhr) {
                    swal('{{ __('Error') }}', '{{ __('An error occurred') }}', 'error');
                }
            });
        });

        $(tableID).on("change", ".status-update", function (event) 
        {
            var id = $(this).data("id");
            event.preventDefault();

            $.ajax({
                url: "{{ route('postcodeRegionStatusUpdate') }}",
                type: "POST",
                data: {
                    id: id,
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                dataType: "json",
                success: function(response) {
                    if(response.status){
                        var message = response.message;
                        if (Array.isArray(message)) { message = message.join('\n'); }
                        swal('{{ __('Success') }}', message, 'success');
                    } else {
                        var message = response.message;
                        if (Array.isArray(message)) { message = message.join('\n'); }
                        swal('{{ __('Error') }}', message, 'error');
                        location.reload();
                    }
                },
                error: function(xhr) {
                    swal('{{ __('Error') }}', '{{ __('An error occurred') }}', 'error');
                    location.reload();
                }
            });
        });
    });
</script>
@endsection
