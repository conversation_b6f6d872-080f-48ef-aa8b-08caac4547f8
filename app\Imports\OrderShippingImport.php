<?php

namespace App\Imports;

use App\Models\Order;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class OrderShippingImport implements ToCollection, WithHeadingRow
{
    protected $previewData = [];
    protected $validationErrors = [];

    public function collection(Collection $rows)
    {
        foreach ($rows as $index => $row) {
            $rowNumber = $index + 2; // +2 because of heading row and 0-index
            
            $validationResult = $this->validateRow($row, $rowNumber);
            
            $this->previewData[] = [
                'row_number' => $rowNumber,
                'order_number' => $row['order_number'] ?? '',
                'tracking_number' => $row['tracking_number'] ?? '',
                'delivery_method' => $row['delivery_method'] ?? '',
                'is_valid' => $validationResult['is_valid'],
                'errors' => $validationResult['errors'],
                'order_data' => $validationResult['order_data'] ?? null,
            ];
        }
    }

    protected function validateRow($row, $rowNumber)
    {
        $errors = [];
        $isValid = true;
        $orderData = null;

        // Validate order_number
        if (empty($row['order_number'])) {
            $errors[] = 'Order number is required';
            $isValid = false;
        } else {
            $order = Order::where('order_no', $row['order_number'])->first();
            
            if (!$order) {
                $errors[] = 'Order not found in system';
                $isValid = false;
            } else {
                $orderData = [
                    'id' => $order->id,
                    'current_tracking_number' => $order->tracking_number,
                    'current_delivery_method' => $order->delivery_method,
                    'status' => $order->status,
                ];

                // Check if order can be updated
                if (in_array($order->status, ['cancelled', 'refunded'])) {
                    $errors[] = 'Order status does not allow updates';
                    $isValid = false;
                }
            }
        }

        // Validate delivery_method (optional but must be valid if provided)
        if (!empty($row['delivery_method'])) {
            $validMethods = array_keys(config('staticdata.delivery_method', ['delivery' => 'Delivery', 'pickup' => 'Pickup']));
            if (!in_array(strtolower($row['delivery_method']), $validMethods)) {
                $errors[] = 'Invalid delivery method. Must be: ' . implode(', ', $validMethods);
                $isValid = false;
            }
        }

        // Tracking number validation (optional, no specific format required)
        if (!empty($row['tracking_number'])) {
            if (strlen($row['tracking_number']) > 255) {
                $errors[] = 'Tracking number too long (max 255 characters)';
                $isValid = false;
            }
        }

        // Check if at least one field to update is provided
        if (empty($row['tracking_number']) && empty($row['delivery_method'])) {
            $errors[] = 'At least one field (tracking_number or delivery_method) must be provided';
            $isValid = false;
        }

        return [
            'is_valid' => $isValid,
            'errors' => $errors,
            'order_data' => $orderData,
        ];
    }

    public function getPreviewData()
    {
        return $this->previewData;
    }

    public function getValidationErrors()
    {
        return $this->validationErrors;
    }

    public function getStats()
    {
        $total = count($this->previewData);
        $valid = count(array_filter($this->previewData, fn($item) => $item['is_valid']));
        $invalid = $total - $valid;

        return [
            'total' => $total,
            'valid' => $valid,
            'invalid' => $invalid,
        ];
    }
}
