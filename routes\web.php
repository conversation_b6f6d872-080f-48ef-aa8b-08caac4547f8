<?php

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\Api\ApiBookings;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\PagesController;
use App\Http\Controllers\CouponController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\LalamoveController;
use App\Http\Controllers\Admin\FaqController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\MediaController;
use App\Http\Controllers\Admin\PointController;
use App\Http\Controllers\Admin\TaxesController;
use App\Http\Controllers\Admin\BannerController;
use App\Http\Controllers\Admin\OrdersController;
use App\Http\Controllers\Admin\OrderImportController;
use App\Http\Controllers\Admin\OutletController;
use App\Http\Controllers\Admin\WalletController;
use App\Http\Controllers\Admin\BookingController;
use App\Http\Controllers\Admin\ReviewsController;
use App\Http\Controllers\Admin\VoucherController;
use App\Http\Controllers\Admin\ProductsController;
use App\Http\Controllers\Admin\RetailerController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\PaymentGatewayController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\ProductCategoryController;
use App\Http\Controllers\Admin\AttributesController;
use App\Http\Controllers\Admin\ChainStoreController;
use App\Http\Controllers\Admin\PriceGroupController;
use App\Http\Controllers\Admin\StringTypeController;
use App\Http\Controllers\Admin\ShippingFeeController;
use App\Http\Controllers\Admin\PostcodeRegionController;
use App\Http\Controllers\Admin\StringColorController;
use App\Http\Controllers\Admin\BirthdayGiftController;
use App\Http\Controllers\Admin\NotificationController;
use App\Http\Controllers\Admin\SalesChannelController;
use App\Http\Controllers\ProductSubcategoryController;
use App\Http\Controllers\Admin\ProductBundleController;
use App\Http\Controllers\Admin\StringTensionController;
use App\Http\Controllers\Admin\MembershipTierController;
use App\Http\Controllers\UsersController; // for web use
use App\Http\Controllers\Admin\PurchaseReceiptController;
use App\Http\Controllers\Admin\RedeemedVoucherController;
use App\Http\Controllers\Admin\RetailerProductController;
use App\Http\Controllers\Admin\UserAddressBookController;
use App\Http\Controllers\DoctorController; // for web use
use App\Http\Controllers\Admin\PushNotificationController;
use App\Http\Controllers\Admin\RolesAndPrivilegesController;
use App\Http\Controllers\Admin\NewsAndAnnouncementController;
use App\Http\Controllers\WebStringingController; // for web use
use App\Http\Controllers\Admin\AdsSpendingTransactionController;
use App\Http\Controllers\Admin\RetailerStockTransactionController;
use App\Http\Controllers\Admin\RetailerCreditTransactionController;
use App\Http\Controllers\Admin\UsersController as AdminUsersController;
use App\Http\Controllers\Admin\DoctorController as AdminDoctorController;
use App\Http\Controllers\ProductPreviewController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('test-order-receipt/{id}', [OrdersController::class, 'pdfReceipt'])->name('testOrderPdfReceipt');

// --- Product Preview Routes ---
Route::get('/product-detail/{id}', [ProductPreviewController::class, 'show']);
Route::get('/product-detail/debug/{id}', [ProductPreviewController::class, 'debug']);

Route::get('/linkstorage', function () {
    Artisan::call('storage:link');
});

Route::controller(Controller::class)->group(function () {
    Route::get('generate-random-string/{length?}', 'generateRandomString')->name('generateRandomString');
});

Route::controller(LoginController::class)->group(function () {
    Route::get('/', 'login')->name('/');
    Route::get('forgot-password', 'forgotPassword')->name('forgotPassword');
    Route::post('reset-link', 'resetLink')->name('resetLink');
    Route::get('admin-reset-password-verify/{token?}', 'resetPasswordVerify')->name('resetPasswordVerify');
    Route::post('admin-reset-password', 'resetPassword')->name('resetPassword');
    Route::get('logout', 'logout')->name('logout');
});

Route::controller(LoginController::class)->prefix('retailer')->group(function () {
    Route::get('/', 'login')->name('retailer.login');
    Route::get('forgot-password', 'forgotPassword')->name('retailer.forgotPassword');
    Route::post('reset-link', 'resetLink')->name('retailer.resetLink');
    Route::get('admin-reset-password-verify/{token?}', 'resetPasswordVerify')->name('retailer.resetPasswordVerify');
    Route::post('admin-reset-password', 'resetPassword')->name('retailer.resetPassword');
    Route::get('logout', 'logout')->name('retailer.logout');
});

Route::prefix('lalamove')->group(function () {
    Route::post('/quote', [LalamoveController::class, 'getQuote']);
    Route::post('/order', [LalamoveController::class, 'placeOrder']);
    Route::get('/order/{orderId}', [LalamoveController::class, 'getOrderStatus']);
    Route::put('/order/{orderId}/cancel', [LalamoveController::class, 'cancelOrder']);
    Route::get('/order/{orderId}/driver/{driverId}/location', [LalamoveController::class, 'getDriverLocation']);
    Route::get('/order/{orderId}/driver/{driverId}', [LalamoveController::class, 'getDriverDetails']);
    Route::post('/webhook', [LalamoveController::class, 'handleWebhook']);
});

Route::get('email-birthday-gift/{id}', [Controller::class, 'emailBirthdayGift'])->name('emailBirthdayGift');

Route::middleware('checkLogin')->group(function () {
    Route::controller(LoginController::class)->group(function () {
        Route::post('login', 'checklogin')->name('login');
    });

    Route::controller(DashboardController::class)->group(function () {
        Route::get('index', 'index')->name('index');
    });

    Route::controller(BookingController::class)->group(function () {
        Route::get('bookings/{type}', 'bookings')->name('bookings');
        Route::post('bookings-list', 'bookingsList')->name('bookingsList');
        Route::post('bookings-export', 'bookingsExport')->name('bookingsExport');
        Route::post('bookings-update', 'bookingsUpdate')->name('bookingsUpdate');
        Route::post('bookings-edit', 'bookingsEdit')->name('bookingsEdit');

        Route::get('bookings-detail/{id}', 'bookingsDetail')->name('bookingsDetail');
    });

    Route::controller(AdminController::class)->group(function () {
        Route::get('admins/', 'index')->name('admins');
        Route::post('admins-list', 'listing')->name('adminsList');
        Route::post('admins-detail', 'detail')->name('adminsDetail');
        Route::post('admins-add-update', 'addUpdate')->name('adminsAddUpdate');
        Route::post('admins-status-update', 'statusUpdate')->name('adminsStatusUpdate');
        Route::post('admins-delete', 'delete')->name('adminsDelete');
        Route::post('admins-export', 'export')->name('adminsExport');
        Route::post('admin-ajax/{first?}', 'dropdownList')->name('adminAjax');
    });

    Route::controller(AdminUsersController::class)->group(function () {
        Route::get('users', 'users')->name('users');
        Route::post('users-list', 'usersList')->name('usersList');
        Route::post('users-add-update', 'usersAddUpdate')->name('usersAddUpdate');
        Route::post('users-status-update', 'usersStatusUpdate')->name('usersStatusUpdate');
        Route::post('users-delete', 'usersDelete')->name('usersDelete');
        Route::post('users-export', 'usersExport')->name('usersExport');
        Route::post('users-ajax/{first?}', 'usersDropdownList')->name('usersAjax');
        Route::post('users-detail', 'usersDetail')->name('usersDetail');
    });

    Route::controller(AdminDoctorController::class)->group(function () {
        Route::get('staffs', 'doctors')->name('doctors');
        Route::post('doctors-list', 'doctorsList')->name('doctorsList');
        Route::post('doctors-add-update', 'doctorsAddUpdate')->name('doctorsAddUpdate');
        Route::get('doctors-profile/{id}', 'doctorsDetail')->name('doctorsProfile');
        Route::post('doctors-status-update', 'doctorsStatusUpdate')->name('doctorsStatusUpdate');
        Route::post('doctors-delete', 'doctorsDelete')->name('doctorsDelete');
        Route::post('doctors-export', 'doctorsExport')->name('doctorsExport');
        Route::post('doctors-ajax/{first?}', 'doctorsDropdownList')->name('doctorsAjax');

        Route::post('appointment-slots-add', 'appointmentSlotsAdd')->name('appointmentSlotsAdd');
        Route::post('appointment-slots-view', 'appointmentSlotsView')->name('appointmentSlotsView');
        Route::post('appointment-slots-delete', 'appointmentSlotsDelete')->name('appointmentSlotsDelete');

        Route::post('holidays-add', 'holidaysAdd')->name('holidaysAdd');
        Route::post('holidays-view', 'holidaysView')->name('holidaysView');
        Route::post('holidays-delete', 'holidaysDelete')->name('holidaysDelete');
        Route::post('holidays-export', 'holidaysExport')->name('holidaysExport');
    });

    Route::controller(ReviewsController::class)->group(function () {
        Route::get('reviews', 'index')->name('reviews');
        Route::post('reviews-detail', 'detail')->name('reviewsDetail');
        Route::post('reviews-list', 'listing')->name('reviewsList');
        Route::post('reviews-delete', 'delete')->name('reviewsDelete');
        Route::post('reviews-update', 'reviewUpdate')->name('reviewUpdate');
    });

    Route::controller(RedeemedVoucherController::class)->group(function () {
        Route::get('redeemed-vouchers', 'index')->name('redeemed-vouchers');
        Route::post('redeemed-vouchers-list', 'listing')->name('redeemed-vouchers-list');
        Route::post('redeemed-vouchers-export', 'export')->name('redeemed-vouchers-export');
        Route::post('redeemed-vouchers-used', 'usedUpdate')->name('redeemed-vouchers-used');
        Route::post('redeemed-vouchers-add', 'store')->name('redeemedVouchersAdd');
        Route::get('redeemed-vouchers-get-tier', 'getVoucherBasedOnTier')->name('getVoucherBasedOnTier');
        Route::post('redeemed-vouchers-delete', 'delete')->name('redeemedVouchersDelete');
    });

    Route::controller(WalletController::class)->group(function () {
        Route::get('wallet/{type}', 'wallet')->name('wallet');
        Route::post('wallet-list', 'walletList')->name('walletList');
        Route::post('wallet-export', 'walletExport')->name('walletExport');
        Route::post('wallet-topup', 'walletTopup')->name('walletTopup');
        Route::post('point-update', 'pointUpdate')->name('pointUpdate');
        Route::get('user-wallet/{id}', 'getUserWallet')->name('userWallet');
        Route::post('wallet-update', 'walletUpdate')->name('walletUpdate');
        Route::get('wallet-detail/{id}', 'walletDetail')->name('walletDetail');
    });

    Route::controller(PointController::class)->group(function () {
        Route::get('point', 'point')->name('point');
        Route::post('point-list', 'pointList')->name('pointList');
        Route::post('point-export', 'pointExport')->name('pointExport');
        Route::post('point-update', 'pointUpdate')->name('pointUpdate');
        Route::get('user-point/{id}', 'getUserPoint')->name('userPoint');
        Route::get('point-detail/{id}', 'pointDetail')->name('pointDetail');

        Route::get('point-redemptions', 'pointRedemptions')->name('pointRedemptions');
        Route::post('point-redemptions-list', 'pointRedemptionsList')->name('pointRedemptionsList');
        Route::post('point-redemptions-export', 'pointRedemptionsExport')->name('pointRedemptionsExport');
    });

    Route::controller(RolesAndPrivilegesController::class)->group(function () {
        Route::get('roles-privileges', 'index')->name('rolesPrivileges');
        Route::post('roles-privileges-list', 'listing')->name('rolesPrivilegesList');
        Route::post('roles-privileges-add-update', 'addUpdate')->name('rolesPrivilegesAddUpdate');
        Route::post('roles-privileges-delete', 'delete')->name('rolesPrivilegesDelete');
        Route::post('roles-privileges-export', 'export')->name('rolesPrivilegesExport');
        Route::post('roles-ajax/{first?}', 'dropdownList')->name('rolesPrivilegesAjax');
        Route::get('roles-privileges/feature-enabler', 'featureEnabler')->name('rolesPrivilegesFeatureEnabler');
        Route::post('roles-privileges/feature-enabler-update', 'featureEnablerUpdate')->name('rolesPrivilegesFeatureEnablerUpdate');

        Route::post('roles-privileges/{id}', 'detail')->name('rolesPrivilegesDetail');
        Route::post('roles-privileges-view/{id}', 'viewPrivileges')->name('rolesPrivilegesView');
    });

    Route::controller(FaqController::class)->group(function () {
        Route::get('faqs', 'index')->name('faqs');
        Route::post('faqs-list', 'listing')->name('faqsList');
        Route::post('faqs-add-update', 'addUpdate')->name('faqsAddUpdate');
        Route::post('faqs-status-update', 'statusUpdate')->name('faqsStatusUpdate');
        Route::post('faqs-delete', 'delete')->name('faqsDelete');
        Route::post('faqs-export', 'export')->name('faqsExport');
    });

    Route::controller(SettingsController::class)->group(function () {
        Route::get('settings', 'settings')->name('settings');
        Route::post('settings-update', 'settingsUpdate')->name('settingsUpdate');

        Route::get('payment-list', 'paymentList')->name('paymentList');
        Route::post('payment-update', 'paymentUpdate')->name('paymentUpdate');

        Route::get('change-password', 'changePassword')->name('changePassword');
        Route::post('change-password-update', 'changePasswordUpdate')->name('changePasswordUpdate');
        Route::post('change-personal-info', 'changePersonalInfo')->name('changePersonalInfo');
    });

    Route::controller(PagesController::class)->group(function () {
        Route::get('pages/{type}', 'pagesView')->name('pagesView');
        Route::post('pages-update', 'pagesUpdate')->name('pagesUpdate');
        Route::post('pages-detail', 'pagesDetail')->name('pagesDetail');
    });

    Route::controller(PushNotificationController::class)->group(function () {
        Route::get('push-notifications', 'index')->name('pushNotification');
        Route::post('push-notifications-list', 'listing')->name('pushNotificationList');
        Route::get('push-notification/{id}', 'detail')->name('pushNotificationDetail');
    });

    Route::controller(MembershipTierController::class)->group(function () {
        Route::get('membership', 'index')->name('membershipTier');
        Route::post('membership-list', 'listing')->name('membershipTierList');
        Route::post('membership-detail', 'detail')->name('membershipTierDetail');
        Route::post('membership-add-update', 'addUpdate')->name('membershipTierAddUpdate');
        Route::get('membership-benefit/{membership_tier_id}', 'benefitIndex')->name('membershipTierBenefit');
        Route::post('membership-benefit-list', 'benefitListing')->name('membershipTierBenefitList');
        Route::post('membership-benefit-detail', 'benefitDetail')->name('membershipTierBenefitDetail');
        Route::post('membership-benefit-delete', 'benefitDelete')->name('membershipTierBenefitDelete');
        Route::post('membership-benefit-add-update', 'benefitAddUpdate')->name('membershipTierBenefitAddUpdate');
        Route::post('membership-status-update', 'statusUpdate')->name('membershipTierStatusUpdate');
        Route::post('membership-export', 'export')->name('membershipTierExport');
        Route::post('membership-delete', 'delete')->name('membershipTierDelete');
    });

    Route::controller(BannerController::class)->group(function () {
        Route::get('banner', 'index')->name('banner');
        Route::post('banner-list', 'listing')->name('bannerList');
        Route::post('banner-detail', 'detail')->name('bannerDetail');
        Route::post('banner-add-update', 'addUpdate')->name('bannerAddUpdate');
        Route::post('banner-status-update', 'statusUpdate')->name('bannerStatusUpdate');
        Route::post('banner-delete', 'delete')->name('bannerDelete');
        Route::post('banner-export', 'export')->name('bannerExport');
    });

    Route::controller(StringTypeController::class)->group(function () {
        Route::get('string-type', 'index')->name('stringType');
        Route::post('string-type-list', 'listing')->name('stringTypeList');
        Route::post('string-type-add-update', 'addUpdate')->name('stringTypeAddUpdate');
        Route::get('string-type-colors/{id}', 'getStringTypeColors')->name('getStringTypeColors');
        Route::post('string-type-status-update', 'statusUpdate')->name('stringTypeStatusUpdate');
        Route::post('string-type-delete', 'delete')->name('stringTypeDelete');
        Route::post('string-type-export', 'export')->name('stringTypeExport');
    });

    Route::controller(SalesChannelController::class)->group(function () {
        Route::get('sales-channel', 'index')->name('salesChannel');
        Route::post('sales-channel-list', 'listing')->name('salesChannelList');
        Route::post('sales-channel-detail', 'detail')->name('salesChannelDetail');
        Route::post('sales-channel-add-update', 'addUpdate')->name('salesChannelAddUpdate');
        Route::post('sales-channel-status-update', 'statusUpdate')->name('salesChannelStatusUpdate');
        Route::post('sales-channel-delete', 'delete')->name('salesChannelDelete');
        Route::post('sales-channel-export', 'export')->name('salesChannelExport');
        Route::get('sales-channel/ajax-channel/{first?}', 'ajaxChannel')->name('salesChannelAjaxChannel');
        Route::get('sales-channel/ajax-value/{first?}', 'ajaxValue')->name('salesChannelAjaxValue');
    });

    Route::controller(StringTensionController::class)->group(function () {
        Route::get('string-tension', 'index')->name('stringTension');
        Route::post('string-tension-list', 'listing')->name('stringTensionList');
        Route::post('string-tension-add-update', 'addUpdate')->name('stringTensionAddUpdate');
        Route::post('string-tension-status-update', 'statusUpdate')->name('stringTensionStatusUpdate');
        Route::post('string-tension-delete', 'delete')->name('stringTensionDelete');
        Route::post('string-tension-export', 'export')->name('stringTensionExport');
    });

    Route::controller(StringColorController::class)->group(function () {
        Route::get('string-color', 'index')->name('stringColor');
        Route::post('string-color-list', 'listing')->name('stringColorList');
        Route::post('string-color-add-update', 'addUpdate')->name('stringColorAddUpdate');
        Route::post('string-color-status-update', 'statusUpdate')->name('stringColorStatusUpdate');
        Route::post('string-color-delete', 'delete')->name('stringColorDelete');
        Route::post('string-color-export', 'export')->name('stringColorExport');
    });

    Route::controller(OutletController::class)->group(function () {
        Route::get('store', 'index')->name('store');
        Route::post('store-list', 'listing')->name('storeList');
        Route::get('store-detail/{id}', 'detail')->name('storeDetail');
        Route::post('store-add-update', 'addUpdate')->name('storeAddUpdate');
        Route::post('store-status-update', 'statusUpdate')->name('storeStatusUpdate');
        Route::post('store-delete', 'delete')->name('storeDelete');
        Route::post('store-export', 'export')->name('storeExport');
    });

    Route::controller(NewsAndAnnouncementController::class)->group(function () {
        Route::get('news-and-announcement', 'index')->name('newsAndAnnouncement');
        Route::post('news-and-announcement', 'listing')->name('newsAndAnnouncementList');
        Route::post('news-and-announcement-add-update', 'addUpdate')->name('newsAndAnnouncementAddUpdate');
        Route::post('news-and-announcement-detail', 'detail')->name('newsAndAnnouncementDetail');
        Route::post('news-and-announcement-status-update', 'statusUpdate')->name('newsAndAnnouncementStatusUpdate');
        Route::post('news-and-announcement-delete', 'delete')->name('newsAndAnnouncementDelete');
        Route::post('news-and-announcement-export', 'export')->name('newsAndAnnouncementExport');
    });

    Route::controller(ChainStoreController::class)->group(function () {
        Route::get('chain-store', 'index')->name('chainStore');
        Route::post('chain-store', 'listing')->name('chainStoreList');
        Route::post('chain-store-add-update', 'addUpdate')->name('chainStoreAddUpdate');
        Route::post('chain-store-detail', 'detail')->name('chainStoreDetail');
        Route::post('chain-store-feature-update', 'featureUpdate')->name('chainStoreFeatureUpdate');
        Route::post('chain-store-delete', 'delete')->name('chainStoreDelete');
        Route::post('chain-store-export', 'export')->name('chainStoreExport');
    });

    Route::controller(UserAddressBookController::class)->group(function () {
        Route::get('user-address-book', 'index')->name('userAddressBook');
        Route::post('user-address-book', 'listing')->name('userAddressBookList');
        Route::post('user-address-book-add-update', 'addUpdate')->name('userAddressBookAddUpdate');
        Route::post('user-address-book-detail', 'detail')->name('userAddressBookDetail');
        Route::post('user-address-book-delete', 'delete')->name('userAddressBookDelete');
        Route::post('user-address-book-export', 'export')->name('userAddressBookExport');
        Route::post('user-address-book-ajax', 'dropdownList')->name('userAddressBookAjax');
    });

    Route::controller(RetailerController::class)->group(function () {
        Route::get('retailers/', 'index')->name('retailers');
        Route::post('retailers-list', 'listing')->name('retailersList');
        Route::post('retailers-detail', 'detail')->name('retailersDetail');
        Route::post('retailers-add-update', 'addUpdate')->name('retailersAddUpdate');
        Route::post('retailers-status-update', 'statusUpdate')->name('retailersStatusUpdate');
        Route::post('retailers-delete', 'delete')->name('retailersDelete');
        Route::post('retailers-export', 'export')->name('retailersExport');
        Route::post('retailers-ajax/{first?}', 'dropdownList')->name('retailersAjax');
    });

    Route::controller(ShippingFeeController::class)->group(function () {
        Route::get('shipping-fee', 'index')->name('shippingFee');
        Route::post('shipping-fee', 'listing')->name('shippingFeeList');
        Route::post('shipping-fee-add-update', 'addUpdate')->name('shippingFeeAddUpdate');
        Route::post('shipping-fee-detail', 'detail')->name('shippingFeeDetail');
        Route::post('shipping-fee-delete', 'delete')->name('shippingFeeDelete');
        Route::post('shipping-fee-export', 'export')->name('shippingFeeExport');
    });

    Route::controller(PostcodeRegionController::class)->group(function () {
        Route::get('postcode-region', 'index')->name('postcodeRegion');
        Route::post('postcode-region-list', 'listing')->name('postcodeRegionList');
        Route::post('postcode-region-add-update', 'addUpdate')->name('postcodeRegionAddUpdate');
        Route::post('postcode-region-detail', 'detail')->name('postcodeRegionDetail');
        Route::post('postcode-region-status-update', 'statusUpdate')->name('postcodeRegionStatusUpdate');
        Route::post('postcode-region-delete', 'delete')->name('postcodeRegionDelete');
        Route::post('postcode-region-export', 'export')->name('postcodeRegionExport');
        Route::post('postcode-region-bulk-upload', 'bulkUpload')->name('postcodeRegionBulkUpload');
    });

    Route::controller(MediaController::class)->group(function () {
        Route::post('media-store', 'mediaStore')->name('mediaStore');
    });

    Route::controller(PurchaseReceiptController::class)->group(function () {
        Route::get('purchase-receipt', 'index')->name('purchaseReceipt');
        Route::post('purchase-receipt', 'listing')->name('purchaseReceiptList');
        Route::post('purchase-receipt-add-update', 'addUpdate')->name('purchaseReceiptAddUpdate');
        Route::post('purchase-receipt-approve', 'approveReceipt')->name('purchaseReceiptApprove');
        Route::post('purchase-receipt-reject', 'rejectReceipt')->name('purchaseReceiptReject');
        Route::post('purchase-receipt-detail', 'detail')->name('purchaseReceiptDetail');
        Route::post('purchase-receipt-delete', 'delete')->name('purchaseReceiptDelete');
        Route::post('purchase-receipt-export', 'export')->name('purchaseReceiptExport');
    });

    Route::controller(OrdersController::class)->group(function () {
        Route::get('order', 'index')->name('order');
        Route::get('order-rating', 'ratingIndex')->name('order-rating');
        Route::post('order', 'listing')->name('orderList');
        Route::post('order-rating-listing', 'ratingListing')->name('orderRatingList');
        Route::post('rating-delete', 'ratingDelete')->name('ratingDelete');
        Route::get('order-form/{id?}', 'form')->name('orderForm');
        Route::post('order-add-update', 'addUpdate')->name('orderAddUpdate');
        Route::post('order-export', 'export')->name('orderExport');
        Route::post('order-price-calculate', 'priceCalculation')->name('orderPriceCalculation');
        Route::post('order-check-stock', 'orderCheckStock')->name('orderCheckStock');
        Route::get('order-pdf-receipt/{id}', 'pdfReceipt')->name('orderPdfReceipt');
        Route::get('order-payment-link/{id}', 'orderPaymentLink')->name('orderPaymentLink');
        Route::post('order-check-delivery', 'checkDeliveryAvailability')->name('checkDeliveryAvailability');
    });

    Route::controller(OrderImportController::class)->group(function () {
        Route::get('order-import', 'index')->name('orderImport');
        Route::post('order-import-upload', 'upload')->name('orderImportUpload');
        Route::get('order-import-preview', 'preview')->name('orderImportPreview');
        Route::post('order-import-confirm', 'confirm')->name('orderImportConfirm');
        Route::get('order-import-template', 'downloadTemplate')->name('orderImportTemplate');
        Route::get('order-import-cancel', 'cancel')->name('orderImportCancel');
    });

    Route::controller(BirthdayGiftController::class)->group(function () {
        Route::get('birthday-gift', 'index')->name('birthdayGift');
        Route::post('birthday-gift', 'listing')->name('birthdayGiftList');
        Route::post('birthday-gift-add-update', 'addUpdate')->name('birthdayGiftAddUpdate');
        Route::post('birthday-gift-detail', 'detail')->name('birthdayGiftDetail');
        Route::post('birthday-gift-delete', 'delete')->name('birthdayGiftDelete');
        Route::post('birthday-gift-export', 'export')->name('birthdayGiftExport');
    });

    Route::controller(RetailerProductController::class)->group(function () {
        Route::get('retailer-product', 'index')->name('retailerProduct');
        Route::post('retailer-product', 'listing')->name('retailerProductList');
        Route::post('retailer-product-update', 'update')->name('retailerProductUpdate');
        Route::post('retailer-product-export', 'export')->name('retailerProductExport');
    });

    Route::controller(VoucherController::class)->group(function () {
        Route::get('redemption-vouchers', 'index')->name('redemptionVouchers');
        Route::post('redemption-vouchers-list', 'listing')->name('redemptionVouchersList');
        Route::get('redemption-vouchers-form/{id?}', 'form')->name('redemptionVouchersForm');
        Route::post('redemption-vouchers-add-update', 'addUpdate')->name('redemptionVouchersAddUpdate');
        Route::post('redemption-vouchers-delete', 'delete')->name('redemptionVouchersDelete');
        Route::post('redemption-vouchers-status-update', 'statusUpdate')->name('redemptionVouchersStatusUpdate');
        Route::post('redemption-vouchers-featured-update', 'featuredUpdate')->name('redemptionVouchersFeaturedUpdate');
        Route::post('redemption-vouchers-export', 'export')->name('redemptionVouchersExport');

        Route::post('redemption-vouchers/{id}', 'detail')->name('redemptionVouchersDetail');
        Route::post('user-vouchers-ajax', 'dropdownList')->name('userVoucherAjax');
    });

    Route::controller(ProductCategoryController::class)->group(function () {
        Route::get('product-category', 'index')->name('productCategory');
        Route::post('product-category', 'listing')->name('productCategoryList');
        Route::post('product-category-add-update', 'addUpdate')->name('productCategoryAddUpdate');
        Route::post('product-category-update', 'statusUpdate')->name('productCategoryUpdate');
        Route::post('product-category-detail', 'detail')->name('productCategoryDetail');
        Route::post('product-category-delete', 'delete')->name('productCategoryDelete');
        Route::post('product-category-export', 'export')->name('productCategoryExport');
    });

    Route::controller(ProductSubcategoryController::class)->group(function () {
        Route::get('product-subcategory', 'index')->name('productSubcategory');
        Route::post('product-subcategory', 'listing')->name('productSubcategoryList');
        Route::post('product-subcategory-add-update', 'addUpdate')->name('productSubcategoryAddUpdate');
        Route::post('product-subcategory-update', 'statusUpdate')->name('productSubcategoryUpdate');
        Route::post('product-subcategory-detail', 'detail')->name('productSubcategoryDetail');
        Route::post('product-subcategory-delete', 'delete')->name('productSubcategoryDelete');
        Route::post('product-subcategory-export', 'export')->name('productSubcategoryExport');
    });

    Route::controller(CouponController::class)->group(function () {
        Route::get('coupon', 'index')->name('coupon');
        Route::post('coupon-list', 'listing')->name('couponList');
        Route::get('coupon-form/{id?}', 'form')->name('couponForm');
        Route::post('coupon-add-update', 'addUpdate')->name('couponAddUpdate');
        Route::post('coupon-delete', 'delete')->name('couponDelete');
        Route::post('coupon-status-update', 'statusUpdate')->name('couponStatusUpdate');
        Route::post('coupon-export', 'export')->name('couponExport');
    });

    // optimize
    $route_list = [
        [
            'controller' => ProductBundleController::class,
            'prefix' => 'product-bundle',
            'name' => 'productBundle',
            'additional' => [
                [
                    'prefix' => 'form/{id?}',
                    'function' => 'form',
                    'method' => 'get',
                    'name' => 'Form',
                ],
                [
                    'prefix' => 'product-exist',
                    'function' => 'checkProductExist',
                    'method' => 'get',
                    'name' => 'ProductExist',
                ]
            ]
        ],
        [
            'controller' => PriceGroupController::class,
            'prefix' => 'price-group',
            'name' => 'priceGroups',
        ],
        [
            'controller' => TaxesController::class,
            'prefix' => 'tax',
            'name' => 'taxes',
        ],
        [
            'controller' => AttributesController::class,
            'prefix' => 'attribute',
            'name' => 'attributes',
            'additional' => [
                [
                    'prefix' => 'ajax-option/{first?}',
                    'function' => 'ajaxOption',
                    'method' => 'get',
                    'name' => 'AjaxOption',
                ],
                [
                    'prefix' => 'ajax-value/{first?}',
                    'function' => 'ajaxValue',
                    'method' => 'get',
                    'name' => 'AjaxValue',
                ],
                [
                    'prefix' => 'ajax-latest-sequence',
                    'function' => 'ajaxLatestSequence',
                    'method' => 'get',
                    'name' => 'AjaxLatestSequence',
                ],
            ],
        ],
        [
            'controller' => ProductsController::class,
            'prefix' => 'product',
            'name' => 'products',
            'additional' => [
                [
                    'prefix' => 'form/{id?}',
                    'function' => 'form',
                    'method' => 'get',
                    'name' => 'Form',
                ],
                [
                    'prefix' => 'ajax',
                    'function' => 'ajaxProduct',
                    'method' => 'post',
                    'name' => 'Ajax',
                ],
                [
                    'prefix' => 'ajax-variation',
                    'function' => 'ajaxVariation',
                    'method' => 'get',
                    'name' => 'AjaxVariation',
                ],
                [
                    'prefix' => 'detail',
                    'function' => 'detail',
                    'method' => 'post',
                    'name' => 'Detail',
                ],
                [
                    'prefix' => 'ajax-product-subcategory',
                    'function' => 'ajaxProductSubcategory',
                    'method' => 'get',
                    'name' => 'AjaxProductSubcategory',
                ],
                [
                    'prefix' => 'ajax-get-product',
                    'function' => 'ajaxGetProduct',
                    'method' => 'get',
                    'name' => 'AjaxGetProduct',
                ],
                [
                    'prefix' => 'duplicate/{id}',
                    'function' => 'duplicate',
                    'method' => 'get',
                    'name' => 'Duplicate',
                ],
            ],
        ],
        [
            'controller' => RetailerCreditTransactionController::class,
            'prefix' => 'retailer-credit-transaction',
            'name' => 'retailerCreditTransaction',
        ],
    ];

    foreach ($route_list as $route) {
        Route::controller($route['controller'])->group(function () use ($route) {
            Route::get($route['prefix'], 'index')->name($route['name']);
            Route::post($route['prefix'] . '-list', 'listing')->name($route['name'] . 'List');
            Route::post($route['prefix'] . '-add-update', 'addUpdate')->name($route['name'] . 'AddUpdate');
            Route::post($route['prefix'] . '-delete', 'delete')->name($route['name'] . 'Delete');
            Route::post($route['prefix'] . '-status-update', 'statusUpdate')->name($route['name'] . 'StatusUpdate');
            Route::post($route['prefix'] . '-export', 'export')->name($route['name'] . 'Export');

            if (isset($route['additional'])) {
                foreach ($route['additional'] as $additional) {
                    Route::match([$additional['method']], $route['prefix'] . '-' . $additional['prefix'], $additional['function'])->name($route['name'] . $additional['name']);
                }
            }
        });
    }
    // END optimize

    Route::controller(AdsSpendingTransactionController::class)->group(function () {
        Route::get('ads-spending-transaction', 'index')->name('adsSpendingTransaction');
        Route::post('ads-spending-transaction', 'listing')->name('adsSpendingTransactionList');
        Route::post('ads-spending-transaction-add-update', 'addUpdate')->name('adsSpendingTransactionAddUpdate');
        Route::post('ads-spending-transaction-detail', 'detail')->name('adsSpendingTransactionDetail');
        Route::post('ads-spending-transaction-status-update', 'statusUpdate')->name('adsSpendingTransactionStatusUpdate');
        Route::post('ads-spending-transaction-delete', 'delete')->name('adsSpendingTransactionDelete');
        Route::post('ads-spending-transaction-export', 'export')->name('adsSpendingTransactionExport');
    });

    Route::controller(RetailerStockTransactionController::class)->group(function () {
        Route::get('retailer-stock-transaction', 'index')->name('retailerStockTransaction');
        Route::post('retailer-stock-transaction', 'listing')->name('retailerStockTransactionList');
        Route::post('retailer-stock-transaction-add-update', 'addUpdate')->name('retailerStockTransactionAddUpdate');
        Route::post('retailer-stock-transaction-detail', 'detail')->name('retailerStockTransactionDetail');
        Route::post('retailer-stock-transaction-delete', 'delete')->name('retailerStockTransactionDelete');
        Route::post('retailer-stock-transaction-export', 'export')->name('retailerStockTransactionExport');
        Route::get('retailer-product-stock/{retailer_id}', 'productStock')->name('retailerProductStock');
        Route::post('retailer-product-stock-list/{retailer_id}', 'productStockList')->name('retailerProductStockList');
    });
});

// web
Route::controller(UsersController::class)->group(function () {
    // Route::get('send-email-verification/{token}', 'sendVerificationEmail')->name('emailVerifySend');
    Route::get('email-verification/{token}/{type?}', 'emailVerify')->name('emailVerify');

    Route::post('reset-password', 'passwordReset')->name('passwordReset');
    Route::get('reset-password/{token?}', 'passwordResetVerify')->name('passwordResetVerify');
});

Route::controller(DoctorController::class)->prefix('doctor')->group(function () {
    Route::get('subscription-fee/{ref_no}', 'subscriptionFee')->name('doctor.subscriptionFee');

    // Route::get('send-email-verification/{token}', 'sendVerificationEmail')->name('doctor.emailVerifySend');
    Route::get('email-verification/{token}/{type?}', 'emailVerify')->name('doctor.emailVerify');

    Route::post('reset-password', 'passwordReset')->name('doctor.passwordReset');
    Route::get('reset-password/{token?}', 'passwordResetVerify')->name('doctor.passwordResetVerify');
});

Route::controller(PaymentGatewayController::class)->group(function () {
    Route::get('{gateway}/payment', 'payment')->name('payment');
    Route::any('{gateway}/response', 'response')->name('payment.response');
    Route::any('{gateway}/callback', 'callback')->name('payment.callback');
    Route::any('{gateway}/notification', 'notification')->name('payment.notification');
});

Route::controller(WebStringingController::class)->group(function () {
    Route::get('stringing-form/{outlet_id}', 'form')->name('stringing.form');
    Route::post('stringing-summary', 'summary')->name('stringing.summary');
    Route::post('stringing-create', 'create')->name('stringing.create');
    Route::get('stringing-created/{ref}', 'created')->name('stringing.created');

    Route::namespace('\App\Http\Controllers\Api')
        ->controller(ApiBookings::class)
        ->prefix('bookings')
        ->group(function () {
            Route::post('/available-slots', 'availableSlots')->name('api.booking.slot');
            Route::post('/string-types', 'stringTypes')->name('api.string.types');
            Route::post('/string-tensions', 'stringTensions')->name('api.string.tensions');

            Route::post('/checkout', 'checkoutSummary');
            Route::post('/checkout/validate', 'checkoutValidation');
            Route::post('/create', 'create');
        });

});
