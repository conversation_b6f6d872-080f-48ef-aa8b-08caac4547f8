<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class AssignMemberIds extends Command
{
    protected $signature = 'users:assign-member-ids';
    protected $description = 'Assign member_id to existing users in format MB000001';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->info('Starting to assign member_id to existing users...');

        // Get all users without member_id, ordered by id
        $users = User::whereNull('member_id')
            ->whereNull('deleted_at')
            ->orderBy('id', 'asc')
            ->get();

        if ($users->isEmpty()) {
            $this->info('No users found without member_id.');
            return 0;
        }

        $this->info("Found {$users->count()} users without member_id.");

        $progressBar = $this->output->createProgressBar($users->count());
        $progressBar->start();

        foreach ($users as $user) {
            // Generate member_id
            $memberId = User::generateMemberId();

            // Update the user
            $user->member_id = $memberId;
            $user->save();

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->info("Successfully assigned member_id to {$users->count()} users.");

        return 0;
    }
}
