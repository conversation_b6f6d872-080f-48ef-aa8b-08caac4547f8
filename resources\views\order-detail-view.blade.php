<div class="card-body pt-1">
    <div class="row detail-box">
        {{-- @if (!$is_retailer && $order_type == 'retailer')
            <div class="mt-3 col-md-6">
                <label>{{ __('Retailer') }}</label>
                <div>
                    {{ $result->retailer_own_order->first_name ?? '' }}
                    <div>{{ $result->retailer_own_order->user_name ?? '' }}</div>
                    {{ $result->retailer_own_order->phone_number ?? '' }}
                </div>
            </div>
        @else
            <div class="mt-3 col-md-6">
                <label>{{ __('Customer') }}</label>
                <div>
                    {{ $result->user->name ?? '' }}
                    <div>{{ $result->user->email_address ?? '' }}</div>
                    {{ $result->user->phone_number ?? '' }}
                </div>
            </div>
        @endif --}}

        @if ($result->delivery_method == 'delivery')
        <div class="mt-3 col-md-6">
            <label>{{ __('Delivery Address') }}</label>
            <div>
                @if($result->address)
                    {{ $result->address_recipient }}<br>
                    {{ $result->address_phone }}<br>
                    {{ $result->address }}<br>
                    {{ $result->postcode }}
                    {{ $result->city }},<br>
                    {{ config('staticdata.states.'.$result->state) ?? $result->state }},
                    {{ $result->delivery_country->name ?? '' }}
                @else
                    -
                @endif
            </div>
        </div>
        @else
        <div class="mt-3 col-md-6">
            <label>{{ __('Customer') }}</label>
            <div>
                {{ $result->user->name }}<br>
                {{ $result->user->phone_number }}<br>
                {{ $result->user->email_address }}<br>

            </div>
        </div>
        @endif

        <div class="mt-3 col-md-6">
            <label>{{ __('Store Locator') }}</label>
            <div>
                @if($result->outlet)
                    {{ $result->outlet->name }}<br>
                    {{ $result->outlet->address }}<br>
                @else
                    -
                @endif
            </div>
        </div>

        <div class="mt-3 col-md-6">
            <label>{{ __('Billing Contact') }}</label>
            <div>
                {{ $result->user->name }}<br>
                {{ $result->user->phone_number }}<br>
                {{ $result->user->member_id }}<br>
            </div>
        </div>

        <div class="col-md-12">
            <div class="mt-4 mb-0"><label>{{ __('Products') }}</label></div>
            <div class="table-responsive">
                <table class="table table-bordered table-detail">
                    <thead>
                        <tr>
                            <th width="40%" style="min-width:200px">{{ __('Name') }}</th>
                            <th width="15%" style="min-width:120px" class="text-right">
                                {{ __('Unit Price') }}
                            </th>
                            <th width="15%" style="min-width:120px" class="text-right">
                                {{ __('Unit Weight') }}
                            </th>
                            <th width="15%" style="min-width:120px" class="text-right">
                                {{ __('Total Weight') }}
                            </th>
                            <th width="15%" style="min-width:120px" class="text-right">
                                {{ __('Tax Amount') }}
                            </th>
                            <th width="10%" style="min-width:100px" class="text-center">{{ __('Quantity') }}</th>
                            <th width="15%" style="min-width:120px" class="text-right">{{ __('Total') }}</th>
                            @if(!$id)
                            <th width="5%" style="min-width:80px" class="text-center">{{ __('Action') }}</th>
                            @endif
                        </tr>
                    </thead>
                    <tbody>
                        @if (count($result->orderItems) > 0)
                            @foreach ($result->normal_items as $key => $item)
                                <tr>
                                    <td>
                                        @if($item->is_birthday)
                                            <span class="badge badge-success badge-mini">{{ __('Birthday Gift') }}</span>
                                        @elseif($item->voucher_id)
                                            <span class="badge badge-success badge-mini">{{ __('Voucher Gift') }}</span>
                                        @endif

                                        {{ $item->product_name }}
                                        @if($item->variation_name)
                                            - {{ $item->variation_name }}
                                        @endif
                                    </td>
                                    <td class="text-right">{{ $result->currency }} {{ number_format($item->price_per_quantity, 2) }}</td>
                                    <td class="text-right">{{ $item->unit_weight }} KG</td>
                                    <td class="text-right">{{ $item->total_weight }} KG</td>
                                    <td class="text-right">{{ $result->currency }} {{ number_format($item->unit_tax, 2) }}</td>
                                    <td class="text-center">{{ $item->quantity }}</td>
                                    <td class="text-right">{{ $result->currency }} {{ number_format($item->total + $item->total_tax, 2) }}</td>
                                </tr>
                            @endforeach

                            @if($result->bundle_items->count() > 0)
                                @foreach($result->bundles as $bundle_detail)
                                    <tr>
                                        <td>
                                            <span class="badge badge-info badge-mini">{{ __('Bundle') }}</span>
                                            {{ $bundle_detail->bundle_name }}
                                        </td>
                                        <td class="text-right">
                                            {{ $result->currency }} {{ number_format($bundle_detail->bundle_price, 2) }}
                                        </td>
                                        <td class="text-right">{{ $result->currency }} {{ number_format(0, 2) }}</td>
                                        <td class="text-center">1</td>
                                        <td class="text-right">
                                            {{ $result->currency }} {{ number_format($bundle_detail->bundle_price, 2) }}
                                        </td>
                                    </tr>

                                    @if($bundle_detail->order_items->count() > 0)
                                        @foreach ($bundle_detail->order_items as $key => $item)
                                            <tr>
                                                <td>
                                                    @if($item->is_gwp)
                                                        <span class="badge badge-success badge-mini">{{ __('Gift') }}</span>
                                                    @elseif($item->is_pwp)
                                                        <span class="badge badge-warning badge-mini">{{ __('PWP') }}</span>
                                                    @endif
                                                    {{ $item->product_name }}
                                                    @if($item->variation_name)
                                                        - {{ $item->variation_name }}
                                                    @endif
                                                </td>
                                                <td class="text-right">
                                                    @if($item->is_pwp)
                                                        {{ $result->currency }} {{ number_format($item->price_per_quantity, 2) }}
                                                    @endif
                                                </td>
                                                <td class="text-right">
                                                    @if($item->is_pwp)
                                                        {{ $result->currency }} {{ number_format($item->unit_tax, 2) }}
                                                    @endif
                                                </td>
                                                <td class="text-center">{{ $item->quantity }}</td>
                                                <td class="text-right">
                                                    @if($item->is_pwp)
                                                        {{ $result->currency }} {{ number_format($item->total + $item->total_tax, 2) }}
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    @endif
                                @endforeach
                            @endif
                        @endif
                    </tbody>
                    <tfoot>
                        <tr class="total-box">
                            <td colspan="6" class="text-right font-weight-600">{{ __('Subtotal') }}</td>
                            <td class="text-right">
                                {{ $result->currency }} {{ number_format($result->subtotal ?? 0, 2) }}
                            </td>
                        </tr>
                        <tr class="total-box">
                            <td colspan="6" class="text-right">{{ __('Tax Amount') }}</td>
                            <td class="text-right">
                                {{ $result->currency }} {{ number_format($result->total_tax ?? 0, 2) }}
                            </td>
                        </tr>
                        <tr class="total-box">
                            <td colspan="6" class="text-right">{{ __('Delivery Fee') }}</td>
                            <td class="text-right">
                                @if($result->delivery_discount > 0)
                                    <span style="text-decoration: line-through">{{ $result->currency }} {{ number_format($result->shipping_fee ?? 0, 2) }}</span>
                                    <br>
                                    @if ($result->delivery_discount == 'free')
                                        {{ __('Free') }}
                                    @else
                                        <span> {{ $result->currency }} {{ number_format($result->shipping_fee - $result->delivery_discount ?? 0, 2) }}</span>
                                    @endif
                                @else
                                    {{ $result->currency }} {{ number_format($result->shipping_fee ?? 0, 2) }}
                                @endif
                            </td>
                        </tr>
                        @if ($result->coupon)
                        <tr class="total-box">
                            <td colspan="6" class="text-right">{{ __('Coupon Discount') }}</td>
                            <td class="text-right">
                                - {{ $result->currency }} {{ number_format($result->coupon ?? 0, 2) }}
                            </td>
                        </tr>
                        @endif
                        @if ($result->voucher > 0)
                        <tr class="total-box">
                            <td colspan="6" class="text-right">
                                @if($is_retailer)
                                    {{ __('Discount Amount') }}
                                @else
                                    {{ __('Voucher Amount') }}
                                @endif
                            </td>
                            <td class="text-right">
                                - {{ $result->currency }} {{ number_format($result->voucher ?? 0, 2) }}
                            </td>
                        </tr>
                        @endif
                        <tr class="total-box">
                            <td colspan="6" class="text-right font-weight-600">{{ __('Total') }}</td>
                            <td class="text-right font-weight-600">
                                {{ $result->currency }} {{ number_format($result->total ?? 0, 2) }}
                            </td>
                        </tr>
                        @if($result->user_type == 'customer')
                        <tr class="total-box">
                            <td colspan="6" class="text-right">{{ __('Point Earned') }}</td>
                            <td class="text-right">
                                {{ number_format($result->point_earned ?? 0, 0) }}
                                @if($result->status != 'completed')
                                    <div class="text-info">{{ __('Pending') }}</div>
                                @elseif($result->status == 'cancelled')
                                    <div class="text-danger">{{ __('Cancelled') }}</div>
                                @endif
                            </td>
                        </tr>
                        @endif
                    </tfoot>
                </table>
            </div>

            @if ($result->rating)
            <div class="mt-3 col-md-12">
                <label>{{ __('Review') }}</label>
                <div>
                    {{ $result->rating }} | {{ $result->review }}
                </div>
            </div>
            @endif
            @if($result->voucher_id)
                <div class="mt-3">
                    <label>{{ __('Vouchers') }}</label>
                    <div>{!! $result->voucher_name !!}</div>
                </div>
            @endif
        </div>

        @if($is_retailer && $order_type == 'retailer')
            <div class="mt-3 col-md-6">
                <label>{{ __('Payment Method') }}</label>
                <div>{{ config('staticdata.retailer_type.'.$result->payment_method) ?? '-' }}</div>
            </div>
            <div class="mt-3 col-md-6">
                <label>{{ __('Status') }}</label>
                <div>{{ config('staticdata.order_status.'.$result->status) ?? '-' }}</div>
            </div>
            <div class="mt-3 col-md-6">
                <label>{{ __('Delivery Method') }}</label>
                <div>{{ $result->delivery_method ?? '-' }}</div>
            </div>
            <div class="mt-3 col-md-6">
                <label>{{ __('Tracking Number') }}</label>
                <div>{{ $result->tracking_number ?? '-' }}</div>
            </div>
            <div class="mt-3 col-md-6">
                <label>{{ __('Receipt') }}</label>
                <div>
                    @if($result->receipt)
                        <a href="{{ $result->receipt }}" target="_blank">{{ __('View Receipt') }}</a>
                    @else
                        -
                    @endif
                </div>
            </div>
            <div class="mt-3 col-md-6">
                <label>{{ __('Remarks') }}</label>
                <div>{{ nl2br($result->remark) }}</div>
            </div>
        @endif
    </div>
</div>
