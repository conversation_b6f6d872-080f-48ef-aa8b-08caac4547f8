<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('delivery_region_postcode', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('delivery_region_id');
            $table->string('postcode', 20);
            $table->timestamps();

            // Indexes
            $table->index('delivery_region_id', 'idx_delivery_region_id');
            $table->index('postcode', 'idx_postcode');
            $table->unique(['delivery_region_id', 'postcode'], 'unique_region_postcode');

            // Foreign key
            $table->foreign('delivery_region_id')->references('id')->on('delivery_region')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('delivery_region_postcode');
    }
};
