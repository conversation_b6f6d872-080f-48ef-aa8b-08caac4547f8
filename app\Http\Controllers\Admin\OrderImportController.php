<?php

namespace App\Http\Controllers\Admin;

use App\Models\Order;
use Illuminate\Http\Request;
use App\Imports\OrderShippingImport;
use App\Models\GlobalFunction;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderImportController extends Controller
{
    public function index()
    {
        if (!checkPermission('edit_orders')) {
            return redirect(route('index'))->with('error', __('You do not have permission to import orders.'));
        }

        return view('order-import');
    }

    public function upload(Request $request)
    {
        if (!checkPermission('edit_orders')) {
            return GlobalFunction::sendSimpleResponse(false, 'You do not have permission to import orders.');
        }

        $request->validate([
            'file' => 'required|file|mimes:csv,xlsx,xls|max:5120', // set 5MB max
        ]);

        try {
            $file = $request->file('file');
            
            // Import and validate
            $import = new OrderShippingImport();
            Excel::import($import, $file);
            
            $previewData = $import->getPreviewData();
            $stats = $import->getStats();

            if ($stats['total'] === 0) {
                return GlobalFunction::sendSimpleResponse(false, 'The uploaded file is empty or has no valid data rows.');
            }

            // Store preview data in session for confirmation
            session(['order_import_preview' => $previewData]);
            session(['order_import_stats' => $stats]);

            return GlobalFunction::sendDataResponse(true, 'File uploaded successfully. Please review the data.', [
                'preview_data' => $previewData,
                'stats' => $stats,
            ]);

        } catch (\Exception $e) {
            Log::error('Order import upload error: ' . $e->getMessage());
            return GlobalFunction::sendSimpleResponse(false, 'Error processing file: ' . $e->getMessage());
        }
    }

    public function preview(Request $request)
    {
        if (!checkPermission('edit_orders')) {
            return redirect(route('index'))->with('error', __('You do not have permission to import orders.'));
        }

        $previewData = session('order_import_preview');
        $stats = session('order_import_stats');

        if (!$previewData) {
            return redirect(route('orderImport'))->with('error', __('No preview data found. Please upload a file first.'));
        }

        return view('order-import-preview', compact('previewData', 'stats'));
    }

    public function confirm(Request $request)
    {
        if (!checkPermission('edit_orders')) {
            return GlobalFunction::sendSimpleResponse(false, 'You do not have permission to import orders.');
        }

        $previewData = session('order_import_preview');
        
        if (!$previewData) {
            return GlobalFunction::sendSimpleResponse(false, 'No preview data found. Please upload a file first.');
        }

        $selectedRows = $request->input('selected_rows', []);
        
        $editedData = $request->input('edited_data', []);

        $successCount = 0;
        $failedCount = 0;
        $failedOrders = [];
        $updatedOrders = [];

        DB::beginTransaction();
        
        try {
            foreach ($previewData as $index => $row) {
                // Skip if not selected (if selections are provided)
                if (!empty($selectedRows) && !in_array($index, $selectedRows)) {
                    continue;
                }

                // Skip invalid rows
                if (!$row['is_valid']) {
                    $failedCount++;
                    $failedOrders[] = [
                        'order_number' => $row['order_number'],
                        'reason' => implode(', ', $row['errors']),
                    ];
                    continue;
                }

                // Use edited data if available, otherwise use original
                $orderNumber = $editedData[$index]['order_number'] ?? $row['order_number'];
                $trackingNumber = $editedData[$index]['tracking_number'] ?? $row['tracking_number'];
                $deliveryMethod = $editedData[$index]['delivery_method'] ?? $row['delivery_method'];

                // Find and update order
                $order = Order::where('order_no', $orderNumber)->first();
                
                if (!$order) {
                    $failedCount++;
                    $failedOrders[] = [
                        'order_number' => $orderNumber,
                        'reason' => 'Order not found',
                    ];
                    continue;
                }

                // Track changes
                $changes = [];

                if (!empty($trackingNumber) && $order->tracking_number != $trackingNumber) {
                    $changes['tracking_number'] = ['old' => $order->tracking_number, 'new' => $trackingNumber];
                    $order->tracking_number = $trackingNumber;
                }

                if (!empty($deliveryMethod) && $order->delivery_method != strtolower($deliveryMethod)) {
                    $changes['delivery_method'] = ['old' => $order->delivery_method, 'new' => strtolower($deliveryMethod)];
                    $order->delivery_method = strtolower($deliveryMethod);
                }

                if (!empty($changes)) {
                    // Fetch and update share link from Lalamove API if tracking number was updated
                    if (isset($changes['tracking_number'])) {
                        $this->updateShareLink($order, $trackingNumber);
                    }
                    
                    $order->save();
                    
                    $successCount++;
                    $updatedOrders[] = [
                        'order_number' => $orderNumber,
                        'order_id' => $order->id,
                        'changes' => $changes,
                    ];

                    // Log the update
                    Log::info('Order bulk update', [
                        'order_id' => $order->id,
                        'order_number' => $orderNumber,
                        'changes' => $changes,
                        'updated_by' => session('user')->user_id ?? null,
                    ]);
                }
            }

            DB::commit();

            // Clear session data
            session()->forget(['order_import_preview', 'order_import_stats']);

            return GlobalFunction::sendDataResponse(true, "Import completed. {$successCount} orders updated successfully.", [
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'updated_orders' => $updatedOrders,
                'failed_orders' => $failedOrders,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Order import confirm error: ' . $e->getMessage());
            return GlobalFunction::sendSimpleResponse(false, 'Error updating orders: ' . $e->getMessage());
        }
    }

    public function downloadTemplate()
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="order_shipping_import_template.csv"',
        ];

        $columns = ['order_number', 'tracking_number', 'delivery_method'];
        $sampleData = [
            ['ORD-20251018-00001', 'TRACK123456789', 'delivery'],
            ['ORD-20251018-00002', 'TRACK987654321', 'pickup'],
            ['ORD-20251018-00003', '', 'delivery'],
        ];

        $callback = function() use ($columns, $sampleData) {
            $file = fopen('php://output', 'w');
            
            // Write headers
            fputcsv($file, $columns);
            
            // Write sample data
            foreach ($sampleData as $row) {
                fputcsv($file, $row);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function cancel()
    {
        // Clear session data
        session()->forget(['order_import_preview', 'order_import_stats']);
        
        return redirect(route('orderImport'))->with('message', __('Import cancelled.'));
    }

    /**
     * Generate and update tracking link based on tracking number
     * 
     * @param Order $order
     * @param string $trackingNumber
     * @return void
     */
    protected function updateShareLink($order, $trackingNumber)
    {
        try {
            // Generate parcelsapp.com tracking link
            $trackingLink = 'https://parcelsapp.com/en/tracking/' . urlencode($trackingNumber);
            
            // Store the tracking link in share_link column
            $order->share_link = $trackingLink;
            
            Log::info('Tracking link generated', [
                'order_id' => $order->id,
                'tracking_number' => $trackingNumber,
                'tracking_link' => $trackingLink,
            ]);
            
        } catch (\Exception $e) {
            // Log the error but don't fail the import
            Log::error('Failed to generate tracking link', [
                'order_id' => $order->id,
                'tracking_number' => $trackingNumber,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
