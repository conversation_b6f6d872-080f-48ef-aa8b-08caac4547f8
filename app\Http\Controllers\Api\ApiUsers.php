<?php

namespace App\Http\Controllers\Api;

use Carbon\Carbon;
use App\Models\Users;
use App\Models\PhoneOtp;
use Illuminate\Http\Request;
use App\Models\GlobalFunction;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class ApiUsers
{
    function __construct(Request $request)
    {
        $this->brandSelector = checkFeatureControl('selector', 'brand');

        $this->brand = null;

        if ($this->brandSelector) {
            $this->brand = $request->route()->parameters['brand'];
        }
    }

    function logout()
    {
        $user = auth()->user();
        $user->tokens()->delete();

        return GlobalFunction::sendSimpleResponse(true, __('Account is logged out.'));
    }

    function delete()
    {
        $user = auth()->user();
        $user->is_active = 0;
        $user->save();

        return GlobalFunction::sendSimpleResponse(true, __('Account is deleted.'));
    }

    function profile(Request $request)
    {
        $user = auth()->user();
        $user = GlobalFunction::getUserData($user, $this->brand);

        return GlobalFunction::sendDataResponse(true, '', $user);
    }

    function updateProfile(Request $request)
    {
        $user = auth()->user();

        if (isset($request->name) && $request->name):
            $user->name = $request->name;
        endif;

        if (isset($request->profile_image) && $request->profile_image):
            $user->profile_image = GlobalFunction::saveFileAndGivePath($request->profile_image);
        endif;

        if (isset($request->email_address)):
            if ($request->email_address) {
                // Check if email is already taken
                $email_exists = Users::where('email_address', $request->email_address)
                    ->where('id', '!=', $user->id)
                    ->first();
                if ($email_exists):
                    return GlobalFunction::sendSimpleResponse(false, __('Email address is already taken.'));
                endif;
                $user->email_address = $request->email_address;
            } else {
                // Set to "-" when email is empty
                $user->email_address = '-';
            }
        endif;

        if (isset($request->phone_number) && $request->phone_number):
            if (!GlobalFunction::validatePhoneNumber($request->phone_number)):
                return GlobalFunction::sendSimpleResponse(false, __('Phone number only allowed numbers and "+" sign as first character only.'));
            endif;
            $user->phone_number = $request->phone_number;
        endif;

        if (isset($request->dob) && $request->dob):
            $user->dob = Carbon::parse($request->dob)->format(config('app.db_date_format'));
        endif;

        $user->save();
        $user = GlobalFunction::getUserData($user, $this->brand);
        return GlobalFunction::sendDataResponse(true, __('Profile updated.'), $user);
    }

    function validateCurrentPassword(Request $request)
    {
        $rules = [
            'current_password' => 'required',
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if ($result):
            return $result;
        endif;

        $user = auth()->user();
        if (!Hash::check($request->current_password, $user->password)):
            return GlobalFunction::sendSimpleResponse(false, __('Current password is incorrect.'));
        endif;

        return GlobalFunction::sendSimpleResponse(true, __('Current password is correct.'));
    }

    function updateNewPassword(Request $request)
    {
        $rules = [
            'new_password' => 'required',
            'confirm_password' => 'required|same:new_password'
        ];
        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);
        if ($result):
            return $result;
        endif;

        $user = auth()->user();
        $user->password = Hash::make($request->new_password);
        $user->save();

        return GlobalFunction::sendSimpleResponse(true, __('Password updated.'));
    }

    public function updatePhoneOtp(Request $request)
    {
        $rules = [
            'phone_number' => 'required',
            'otp' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);
        $result = GlobalFunction::showValidatorMsg($validator);

        if ($result):
            return $result;
        endif;

        $OtpCheck = PhoneOtp::when($this->brand != null, function ($q) {
            $q->where('brand', $this->brand);
        })
            ->where('phone_number', $request->phone_number)
            ->where('otp', $request->otp)
            ->first();

        if (!$OtpCheck):
            return GlobalFunction::sendSimpleResponse(false, __('Invalid OTP.'));
        else:
            $OtpCheck->delete();
        endif;

        return GlobalFunction::sendDataResponse(true, __('OTP Valid'), null);
    }
}
