<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

class PostcodeImport implements ToCollection, WithChunkReading, WithCalculatedFormulas
{
    protected $postcodes = [];
    protected $seenPostcodes = []; // Track duplicates for better performance

    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {
            // Try to get postcode from various possible column positions
            // Support both with and without headers
            $postcode = null;
            
            // If row is an array with numeric keys, get first column
            if (isset($row[0])) {
                $postcode = $row[0];
            }
            
            // If row is an object/collection, try common header names
            if (!$postcode) {
                $possibleKeys = ['postcode', 'postcodes', 'code', 'postal_code', 'zip', 'zipcode'];
                foreach ($possibleKeys as $key) {
                    if (isset($row[$key]) && !empty($row[$key])) {
                        $postcode = $row[$key];
                        break;
                    }
                }
            }
            
            // Clean and validate postcode
            if ($postcode !== null && $postcode !== '') {
                // Convert to string and trim
                $postcode = trim(strval($postcode));
                
                // Remove any formula remnants (just in case)
                $postcode = str_replace('=', '', $postcode);
                
                // Skip empty values and header-like values
                if (!empty($postcode) && 
                    !in_array(strtolower($postcode), ['postcode', 'postcodes', 'code', 'postal_code', 'zip', 'zipcode'])) {
                    
                    // Use hash table for faster duplicate checking
                    if (!isset($this->seenPostcodes[$postcode])) {
                        $this->postcodes[] = $postcode;
                        $this->seenPostcodes[$postcode] = true;
                    }
                }
            }
        }
    }
    
    public function chunkSize(): int
    {
        return 1000; // Process 1000 rows at a time to prevent memory issues
    }

    public function getPostcodes()
    {
        return array_values($this->postcodes);
    }
}
