<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\Admin\ProductsController;

class ProductVariations extends Model
{
    use HasFactory;
    public $table = "product_variations";
    public $timestamps = true;

    protected $fillable = [
        'product_id',
        'attribute_id',
        'sku',
        'price',
        'quantity',
        'retailer_price_my',
        'retailer_price_sg',
        'sequence',
        'is_active',
    ];

    protected static function booted(): void
    {
        static::deleted(function (ProductVariations $variation): void {
            $variation->cartProducts()->delete();
        });

        static::updated(function (ProductVariations $variation): void {
            if ($variation->is_active == 0 || $variation->is_out_of_stock == 1 ) {
                $variation->cartProducts()->delete();
            }
        });
    }

    public function product()
    {
        return $this->belongsTo(Products::class, 'product_id', 'id');
    }

    public function cartProducts()
    {
        return $this->hasMany(CartProducts::class, 'variation_id', 'id');
    }

    public function getVariationNameAttribute()
    {
        return (new ProductsController)->getVariationName($this->attribute_id);
    }

    public function getVariationNameWithOptionAttribute()
    {
        return (new ProductsController)->getVariationName($this->attribute_id, true);
    }

    public function getImageUrlAttribute()
    {
        if($this->image):
            return GlobalFunction::createMediaUrl($this->image);
        else:
            return $this->product->single_image_url;
        endif;
    }
}